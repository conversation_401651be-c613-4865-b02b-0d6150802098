"""
Clipboard management utilities for text insertion and manipulation.
"""

import time
from typing import Op<PERSON>
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

try:
    from pynput.keyboard import Key, Controller
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False
    print("Warning: pynput not available. Clipboard operations may be limited.")


class ClipboardManager:
    """Manages clipboard operations and text insertion."""
    
    def __init__(self):
        """Initialize clipboard manager."""
        self.keyboard = Controller() if PYNPUT_AVAILABLE else None
        self.app = QApplication.instance()
        self.clipboard = self.app.clipboard() if self.app else None
        
        # State tracking
        self.last_clipboard_content = ""
        self.insertion_delay = 0.1  # seconds
    
    def get_clipboard_text(self) -> str:
        """
        Get current clipboard text.
        
        Returns:
            Clipboard text content
        """
        if not self.clipboard:
            return ""
        
        try:
            return self.clipboard.text()
        except Exception as e:
            print(f"Error reading clipboard: {e}")
            return ""
    
    def set_clipboard_text(self, text: str) -> bool:
        """
        Set clipboard text content.
        
        Args:
            text: Text to set in clipboard
            
        Returns:
            True if successful
        """
        if not self.clipboard:
            return False
        
        try:
            self.clipboard.setText(text)
            return True
        except Exception as e:
            print(f"Error setting clipboard: {e}")
            return False
    
    def get_selected_text(self) -> str:
        """
        Get currently selected text from clipboard selection.
        
        Returns:
            Selected text or empty string
        """
        if not self.clipboard:
            return ""
        
        try:
            return self.clipboard.text(mode=self.clipboard.Selection)
        except Exception as e:
            print(f"Error reading selection: {e}")
            return ""
    
    def backup_clipboard(self) -> str:
        """
        Backup current clipboard content.
        
        Returns:
            Current clipboard content
        """
        self.last_clipboard_content = self.get_clipboard_text()
        return self.last_clipboard_content
    
    def restore_clipboard(self) -> bool:
        """
        Restore previously backed up clipboard content.
        
        Returns:
            True if successful
        """
        if self.last_clipboard_content:
            return self.set_clipboard_text(self.last_clipboard_content)
        return False
    
    def insert_text(self, text: str, restore_clipboard: bool = True) -> bool:
        """
        Insert text at current cursor position using clipboard.
        
        Args:
            text: Text to insert
            restore_clipboard: Whether to restore original clipboard content
            
        Returns:
            True if successful
        """
        if not self.keyboard or not text:
            return False
        
        try:
            # Backup current clipboard if requested
            original_content = ""
            if restore_clipboard:
                original_content = self.backup_clipboard()
            
            # Set text to clipboard
            if not self.set_clipboard_text(text):
                return False
            
            # Small delay to ensure clipboard is updated
            time.sleep(self.insertion_delay)
            
            # Paste using Ctrl+V
            self.keyboard.press(Key.ctrl)
            self.keyboard.press('v')
            self.keyboard.release('v')
            self.keyboard.release(Key.ctrl)
            
            # Restore original clipboard content if requested
            if restore_clipboard and original_content:
                # Delay before restoring to avoid interference
                QTimer.singleShot(500, lambda: self.set_clipboard_text(original_content))
            
            return True
            
        except Exception as e:
            print(f"Error inserting text: {e}")
            return False
    
    def replace_selected_text(self, new_text: str, restore_clipboard: bool = True) -> bool:
        """
        Replace currently selected text with new text.
        
        Args:
            new_text: Text to replace selection with
            restore_clipboard: Whether to restore original clipboard content
            
        Returns:
            True if successful
        """
        if not self.keyboard or not new_text:
            return False
        
        try:
            # Backup current clipboard if requested
            original_content = ""
            if restore_clipboard:
                original_content = self.backup_clipboard()
            
            # Set new text to clipboard
            if not self.set_clipboard_text(new_text):
                return False
            
            # Small delay to ensure clipboard is updated
            time.sleep(self.insertion_delay)
            
            # Paste to replace selection
            self.keyboard.press(Key.ctrl)
            self.keyboard.press('v')
            self.keyboard.release('v')
            self.keyboard.release(Key.ctrl)
            
            # Restore original clipboard content if requested
            if restore_clipboard and original_content:
                QTimer.singleShot(500, lambda: self.set_clipboard_text(original_content))
            
            return True
            
        except Exception as e:
            print(f"Error replacing selected text: {e}")
            return False
    
    def copy_text(self) -> str:
        """
        Copy currently selected text using Ctrl+C.
        
        Returns:
            Copied text
        """
        if not self.keyboard:
            return ""
        
        try:
            # Clear clipboard first
            self.set_clipboard_text("")
            
            # Copy selection
            self.keyboard.press(Key.ctrl)
            self.keyboard.press('c')
            self.keyboard.release('c')
            self.keyboard.release(Key.ctrl)
            
            # Small delay for copy operation
            time.sleep(self.insertion_delay)
            
            # Return copied text
            return self.get_clipboard_text()
            
        except Exception as e:
            print(f"Error copying text: {e}")
            return ""
    
    def select_all_and_copy(self) -> str:
        """
        Select all text and copy it.
        
        Returns:
            Copied text
        """
        if not self.keyboard:
            return ""
        
        try:
            # Select all
            self.keyboard.press(Key.ctrl)
            self.keyboard.press('a')
            self.keyboard.release('a')
            self.keyboard.release(Key.ctrl)
            
            # Small delay
            time.sleep(self.insertion_delay)
            
            # Copy
            return self.copy_text()
            
        except Exception as e:
            print(f"Error selecting all and copying: {e}")
            return ""
    
    def type_text(self, text: str, delay: float = 0.01) -> bool:
        """
        Type text character by character (slower but more reliable).
        
        Args:
            text: Text to type
            delay: Delay between characters
            
        Returns:
            True if successful
        """
        if not self.keyboard or not text:
            return False
        
        try:
            for char in text:
                self.keyboard.type(char)
                if delay > 0:
                    time.sleep(delay)
            
            return True
            
        except Exception as e:
            print(f"Error typing text: {e}")
            return False
    
    def send_key(self, key) -> bool:
        """
        Send a single key press.
        
        Args:
            key: Key to send
            
        Returns:
            True if successful
        """
        if not self.keyboard:
            return False
        
        try:
            self.keyboard.press(key)
            self.keyboard.release(key)
            return True
            
        except Exception as e:
            print(f"Error sending key: {e}")
            return False
    
    def send_key_combination(self, *keys) -> bool:
        """
        Send a key combination.
        
        Args:
            *keys: Keys to press simultaneously
            
        Returns:
            True if successful
        """
        if not self.keyboard:
            return False
        
        try:
            # Press all keys
            for key in keys:
                self.keyboard.press(key)
            
            # Release all keys in reverse order
            for key in reversed(keys):
                self.keyboard.release(key)
            
            return True
            
        except Exception as e:
            print(f"Error sending key combination: {e}")
            return False
    
    def is_available(self) -> bool:
        """
        Check if clipboard operations are available.
        
        Returns:
            True if available
        """
        return PYNPUT_AVAILABLE and self.keyboard is not None and self.clipboard is not None
