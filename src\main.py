"""
Main entry point for Quill AI Writing Assistant.
"""

import sys
import os
import logging
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QSystemTrayIcon, QMenu, QAction
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QIcon

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ui.main_window import MainWindow
from src.utils.config import config


class QuillApplication:
    """Main application class for Quill."""
    
    def __init__(self):
        """Initialize the application."""
        self.app = None
        self.main_window = None
        self.tray_icon = None
        
        # Setup logging
        self._setup_logging()
        
        # Initialize Qt application
        self._init_qt_app()
        
        # Create main window
        self._create_main_window()
        
        # Setup system tray
        self._setup_system_tray()
        
        # Setup global hotkeys
        self._setup_global_hotkeys()
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        log_level = config.get('logging.level', 'INFO')
        log_file = config.get('logging.file', 'logs/quill.log')
        
        # Create logs directory if it doesn't exist
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_level.upper(), logging.INFO),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Quill AI Writing Assistant starting...")
    
    def _init_qt_app(self) -> None:
        """Initialize Qt application."""
        self.app = QApplication(sys.argv)
        self.app.setApplicationName(config.get('app.name', 'Quill'))
        self.app.setApplicationVersion(config.get('app.version', '2.0.0'))
        self.app.setQuitOnLastWindowClosed(False)  # Keep running in system tray
        
        # Set application icon
        icon_path = project_root / "assets" / "icon.png"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))
    
    def _create_main_window(self) -> None:
        """Create the main window."""
        try:
            self.main_window = MainWindow()
            self.logger.info("Main window created successfully")
        except Exception as e:
            self.logger.error(f"Failed to create main window: {e}")
            sys.exit(1)
    
    def _setup_system_tray(self) -> None:
        """Setup system tray icon and menu."""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            self.logger.warning("System tray is not available")
            return
        
        # Create tray icon
        self.tray_icon = QSystemTrayIcon(self.app)
        
        # Set icon
        icon_path = project_root / "assets" / "tray_icon.png"
        if icon_path.exists():
            self.tray_icon.setIcon(QIcon(str(icon_path)))
        else:
            # Use default icon
            self.tray_icon.setIcon(self.app.style().standardIcon(
                self.app.style().SP_ComputerIcon
            ))
        
        # Create context menu
        tray_menu = QMenu()
        
        # Show/Hide action
        show_action = QAction("Show Assistant", self.app)
        show_action.triggered.connect(self._toggle_main_window)
        tray_menu.addAction(show_action)
        
        tray_menu.addSeparator()
        
        # Status action
        status_action = QAction("Status: Ready", self.app)
        status_action.setEnabled(False)
        tray_menu.addAction(status_action)
        
        # Settings action
        settings_action = QAction("Settings", self.app)
        settings_action.triggered.connect(self._show_settings)
        tray_menu.addAction(settings_action)
        
        tray_menu.addSeparator()
        
        # Quit action
        quit_action = QAction("Quit", self.app)
        quit_action.triggered.connect(self._quit_application)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        
        # Connect signals
        self.tray_icon.activated.connect(self._tray_icon_activated)
        
        # Show tray icon
        self.tray_icon.show()
        self.tray_icon.showMessage(
            "Quill AI Assistant",
            "Assistant is running in the background",
            QSystemTrayIcon.Information,
            3000
        )
        
        # Update status periodically
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_tray_status)
        self.status_timer.start(5000)  # Update every 5 seconds
    
    def _setup_global_hotkeys(self) -> None:
        """Setup global hotkeys."""
        # Global hotkeys are handled by the keyboard monitor
        # in the assistant, so we just need to connect to the signals
        if self.main_window and self.main_window.assistant:
            assistant = self.main_window.assistant
            assistant.keyboard_monitor.hotkey_triggered.connect(self._handle_global_hotkey)
    
    def _handle_global_hotkey(self, action: str) -> None:
        """
        Handle global hotkey activation.
        
        Args:
            action: Hotkey action name
        """
        if action == "toggle_assistant":
            self._toggle_main_window()
        elif action == "quick_complete":
            if self.main_window:
                self.main_window.assistant.trigger_manual_completion()
    
    def _toggle_main_window(self) -> None:
        """Toggle main window visibility."""
        if self.main_window:
            if self.main_window.isVisible():
                self.main_window.hide()
            else:
                self.main_window.show_at_cursor()
    
    def _tray_icon_activated(self, reason) -> None:
        """
        Handle tray icon activation.
        
        Args:
            reason: Activation reason
        """
        if reason == QSystemTrayIcon.DoubleClick:
            self._toggle_main_window()
    
    def _update_tray_status(self) -> None:
        """Update tray icon status."""
        if not self.tray_icon or not self.main_window:
            return
        
        try:
            # Get assistant status
            assistant = self.main_window.assistant
            status = assistant.get_status()
            
            # Update tooltip
            tooltip = f"Quill AI Assistant\nStatus: {status['current_status']}"
            if status['active_requests'] > 0:
                tooltip += f"\nActive requests: {status['active_requests']}"
            
            self.tray_icon.setToolTip(tooltip)
            
            # Update menu status
            menu = self.tray_icon.contextMenu()
            if menu:
                actions = menu.actions()
                for action in actions:
                    if action.text().startswith("Status:"):
                        action.setText(f"Status: {status['current_status']}")
                        break
                        
        except Exception as e:
            self.logger.error(f"Error updating tray status: {e}")
    
    def _show_settings(self) -> None:
        """Show settings dialog."""
        # TODO: Implement settings dialog
        self.tray_icon.showMessage(
            "Settings",
            "Settings dialog not yet implemented",
            QSystemTrayIcon.Information,
            2000
        )
    
    def _quit_application(self) -> None:
        """Quit the application."""
        self.logger.info("Quitting application...")
        
        # Cleanup main window
        if self.main_window:
            self.main_window.close()
        
        # Hide tray icon
        if self.tray_icon:
            self.tray_icon.hide()
        
        # Quit application
        self.app.quit()
    
    def run(self) -> int:
        """
        Run the application.
        
        Returns:
            Exit code
        """
        try:
            # Show main window initially
            if self.main_window:
                self.main_window.show()
            
            self.logger.info("Application started successfully")
            
            # Run event loop
            return self.app.exec_()
            
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            return 1
        finally:
            self.logger.info("Application shutting down")


def main() -> int:
    """
    Main entry point.
    
    Returns:
        Exit code
    """
    try:
        # Create and run application
        app = QuillApplication()
        return app.run()
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        return 0
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
