# Quill Configuration
app:
  name: "Quill - AI Writing Assistant"
  version: "2.0.0"
  window_title: "✏️ Writing Assistant"

# LLM Configuration
llm:
  model: "hf.co/unsloth/Qwen3-0.6B-GGUF:Q4_K_M"
  base_url: "http://localhost:11434"
  timeout: 30
  max_tokens: 512
  temperature: 0.7
  top_p: 0.9
  stream: true
  
  # Performance optimizations
  num_ctx: 2048
  num_predict: 256
  repeat_penalty: 1.1
  
# Text Processing
text:
  buffer_size: 2000
  min_context_length: 10
  max_context_length: 500
  debounce_delay: 500  # milliseconds
  
# UI Configuration
ui:
  theme: "dark"
  opacity: 0.95
  window_size:
    width: 400
    height: 300
  position:
    x: 100
    y: 100
  
  # Colors
  colors:
    primary: "#4a9eff"
    secondary: "#45a165"
    accent: "#9b59b6"
    background: "rgba(40, 44, 52, 0.95)"
    text: "#ffffff"
    border: "#3d3d3d"

# Keyboard Monitoring
keyboard:
  enabled: true
  trigger_keys: ["space", "enter", "tab"]
  hotkeys:
    toggle_assistant: "ctrl+shift+q"
    quick_complete: "ctrl+shift+c"
    rephrase: "ctrl+shift+r"

# Performance
performance:
  cache_size: 100
  max_concurrent_requests: 3
  request_timeout: 10
  
# Logging
logging:
  level: "INFO"
  file: "logs/quill.log"
  max_size: "10MB"
  backup_count: 5
