"""
Configuration management for Quill AI Writing Assistant.
Handles loading and accessing configuration from YAML files.
"""

import os
import yaml
from typing import Any, Dict, Optional
from pathlib import Path


class Config:
    """Singleton configuration manager."""
    
    _instance: Optional['Config'] = None
    _config: Dict[str, Any] = {}
    
    def __new__(cls) -> 'Config':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._load_config()
        return cls._instance
    
    def _load_config(self) -> None:
        """Load configuration from YAML file."""
        try:
            # Get the project root directory
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config" / "settings.yaml"
            
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as file:
                    self._config = yaml.safe_load(file) or {}
            else:
                # Fallback to default configuration
                self._config = self._get_default_config()
                
        except Exception as e:
            print(f"Error loading configuration: {e}")
            self._config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration if file is not found."""
        return {
            'app': {
                'name': 'Quill - AI Writing Assistant',
                'version': '2.0.0',
                'window_title': '✏️ Writing Assistant'
            },
            'llm': {
                'model': 'hf.co/unsloth/Qwen3-0.6B-GGUF:Q4_K_M',
                'base_url': 'http://localhost:11434',
                'timeout': 30,
                'max_tokens': 512,
                'temperature': 0.7,
                'top_p': 0.9,
                'stream': True,
                'num_ctx': 2048,
                'num_predict': 256,
                'repeat_penalty': 1.1
            },
            'text': {
                'buffer_size': 2000,
                'min_context_length': 10,
                'max_context_length': 500,
                'debounce_delay': 500
            },
            'ui': {
                'theme': 'dark',
                'opacity': 0.95,
                'window_size': {'width': 400, 'height': 300},
                'position': {'x': 100, 'y': 100},
                'colors': {
                    'primary': '#4a9eff',
                    'secondary': '#45a165',
                    'accent': '#9b59b6',
                    'background': 'rgba(40, 44, 52, 0.95)',
                    'text': '#ffffff',
                    'border': '#3d3d3d'
                }
            },
            'keyboard': {
                'enabled': True,
                'trigger_keys': ['space', 'enter', 'tab'],
                'hotkeys': {
                    'toggle_assistant': 'ctrl+shift+q',
                    'quick_complete': 'ctrl+shift+c',
                    'rephrase': 'ctrl+shift+r'
                }
            },
            'performance': {
                'cache_size': 100,
                'max_concurrent_requests': 3,
                'request_timeout': 10
            },
            'logging': {
                'level': 'INFO',
                'file': 'logs/quill.log',
                'max_size': '10MB',
                'backup_count': 5
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key: Configuration key in dot notation (e.g., 'llm.model')
            default: Default value if key is not found
            
        Returns:
            Configuration value or default
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        Set configuration value using dot notation.
        
        Args:
            key: Configuration key in dot notation
            value: Value to set
        """
        keys = key.split('.')
        config = self._config
        
        # Navigate to the parent of the target key
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
    
    def reload(self) -> None:
        """Reload configuration from file."""
        self._load_config()
    
    @property
    def all(self) -> Dict[str, Any]:
        """Get all configuration as dictionary."""
        return self._config.copy()


# Global configuration instance
config = Config()
