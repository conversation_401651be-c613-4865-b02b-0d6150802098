"""
Optimized keyboard monitoring with intelligent text capture.
"""

import time
from typing import Optional, Callable, Set, List
from threading import Thread, Event
from PyQt5.QtCore import QObject, pyqtSignal

try:
    from pynput import keyboard
    from pynput.keyboard import Key, Listener
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False
    print("Warning: pynput not available. Keyboard monitoring disabled.")

from ..utils.config import config


class KeyboardMonitor(QObject):
    """
    Optimized keyboard monitor that intelligently captures text input
    and triggers completion suggestions.
    """
    
    # Signals
    text_captured = pyqtSignal(str)
    hotkey_triggered = pyqtSignal(str)
    
    def __init__(self, text_buffer=None):
        """
        Initialize keyboard monitor.
        
        Args:
            text_buffer: TextBuffer instance to append captured text
        """
        super().__init__()
        
        if not PYNPUT_AVAILABLE:
            return
        
        self.text_buffer = text_buffer
        self.running = False
        self.listener: Optional[Listener] = None
        self.stop_event = Event()
        
        # Configuration
        self.enabled = config.get('keyboard.enabled', True)
        self.trigger_keys = set(config.get('keyboard.trigger_keys', ['space', 'enter', 'tab']))
        self.hotkeys = config.get('keyboard.hotkeys', {})
        
        # State tracking
        self.current_modifiers: Set[Key] = set()
        self.last_key_time = 0
        self.typing_session_active = False
        self.idle_threshold = 2.0  # seconds
        
        # Performance optimization
        self.capture_enabled = True
        self.last_capture_time = 0
        self.min_capture_interval = 0.1  # minimum seconds between captures
        
        # Hotkey combinations
        self.hotkey_combinations = self._parse_hotkeys()
    
    def _parse_hotkeys(self) -> dict:
        """Parse hotkey strings into key combinations."""
        combinations = {}
        
        for action, hotkey_str in self.hotkeys.items():
            keys = []
            parts = hotkey_str.lower().split('+')
            
            for part in parts:
                part = part.strip()
                if part == 'ctrl':
                    keys.append(Key.ctrl_l)
                elif part == 'shift':
                    keys.append(Key.shift_l)
                elif part == 'alt':
                    keys.append(Key.alt_l)
                elif part == 'cmd':
                    keys.append(Key.cmd)
                elif len(part) == 1:
                    keys.append(part)
                else:
                    # Handle special keys
                    special_keys = {
                        'space': Key.space,
                        'enter': Key.enter,
                        'tab': Key.tab,
                        'esc': Key.esc,
                        'backspace': Key.backspace
                    }
                    if part in special_keys:
                        keys.append(special_keys[part])
            
            if keys:
                combinations[action] = keys
        
        return combinations
    
    def start(self) -> bool:
        """
        Start keyboard monitoring.
        
        Returns:
            True if started successfully
        """
        if not PYNPUT_AVAILABLE or not self.enabled:
            return False
        
        if self.running:
            return True
        
        try:
            self.running = True
            self.stop_event.clear()
            
            self.listener = Listener(
                on_press=self._on_key_press,
                on_release=self._on_key_release
            )
            self.listener.start()
            
            return True
            
        except Exception as e:
            print(f"Error starting keyboard monitor: {e}")
            self.running = False
            return False
    
    def stop(self) -> None:
        """Stop keyboard monitoring."""
        if not self.running:
            return
        
        self.running = False
        self.stop_event.set()
        
        if self.listener:
            self.listener.stop()
            self.listener = None
        
        self.current_modifiers.clear()
    
    def _on_key_press(self, key) -> None:
        """Handle key press events."""
        if not self.running:
            return
        
        current_time = time.time()
        self.last_key_time = current_time
        
        try:
            # Track modifier keys
            if key in [Key.ctrl_l, Key.ctrl_r, Key.shift_l, Key.shift_r, 
                      Key.alt_l, Key.alt_r, Key.cmd]:
                self.current_modifiers.add(key)
                return
            
            # Check for hotkey combinations
            if self._check_hotkeys(key):
                return
            
            # Handle text input
            if self.capture_enabled and self._should_capture_key(key):
                self._handle_text_input(key, current_time)
                
        except Exception as e:
            print(f"Error in key press handler: {e}")
    
    def _on_key_release(self, key) -> None:
        """Handle key release events."""
        if not self.running:
            return
        
        try:
            # Remove modifier keys
            if key in self.current_modifiers:
                self.current_modifiers.discard(key)
                
        except Exception as e:
            print(f"Error in key release handler: {e}")
    
    def _check_hotkeys(self, key) -> bool:
        """
        Check if current key combination matches any hotkeys.
        
        Args:
            key: Current key pressed
            
        Returns:
            True if hotkey was triggered
        """
        for action, key_combo in self.hotkey_combinations.items():
            if self._matches_combination(key, key_combo):
                self.hotkey_triggered.emit(action)
                return True
        
        return False
    
    def _matches_combination(self, key, combination: List) -> bool:
        """
        Check if current key state matches a hotkey combination.
        
        Args:
            key: Current key
            combination: List of keys in combination
            
        Returns:
            True if combination matches
        """
        # Check if all modifier keys are pressed
        required_modifiers = [k for k in combination if isinstance(k, Key)]
        required_char = [k for k in combination if isinstance(k, str)]
        
        # Check modifiers
        for mod in required_modifiers:
            if mod not in self.current_modifiers:
                return False
        
        # Check character key
        if required_char:
            if hasattr(key, 'char') and key.char:
                return key.char.lower() == required_char[0].lower()
            else:
                return key == required_char[0]
        
        return len(required_modifiers) > 0 and not required_char
    
    def _should_capture_key(self, key) -> bool:
        """
        Determine if key should be captured for text input.
        
        Args:
            key: Key to check
            
        Returns:
            True if should capture
        """
        # Don't capture if modifiers are pressed (except shift)
        active_modifiers = self.current_modifiers - {Key.shift_l, Key.shift_r}
        if active_modifiers:
            return False
        
        # Capture printable characters
        if hasattr(key, 'char') and key.char and key.char.isprintable():
            return True
        
        # Capture specific keys
        capture_keys = {Key.space, Key.enter, Key.tab, Key.backspace}
        return key in capture_keys
    
    def _handle_text_input(self, key, current_time: float) -> None:
        """
        Handle text input and update buffer.
        
        Args:
            key: Input key
            current_time: Current timestamp
        """
        # Rate limiting
        if current_time - self.last_capture_time < self.min_capture_interval:
            return
        
        self.last_capture_time = current_time
        
        # Convert key to text
        text = self._key_to_text(key)
        if not text:
            return
        
        # Update typing session state
        if not self.typing_session_active:
            self.typing_session_active = True
        
        # Add to text buffer
        if self.text_buffer:
            self.text_buffer.append(text)
        
        # Check for trigger conditions
        if self._should_trigger_completion(key, text):
            if self.text_buffer:
                context = self.text_buffer.get_context()
                if context:
                    self.text_captured.emit(context)
    
    def _key_to_text(self, key) -> str:
        """
        Convert key to text representation.
        
        Args:
            key: Input key
            
        Returns:
            Text representation
        """
        if hasattr(key, 'char') and key.char:
            return key.char
        elif key == Key.space:
            return ' '
        elif key == Key.enter:
            return '\n'
        elif key == Key.tab:
            return '\t'
        elif key == Key.backspace:
            # Handle backspace by removing last character
            if self.text_buffer:
                current = self.text_buffer.get()
                if current:
                    # Remove last character
                    self.text_buffer.buffer = current[:-1]
            return ''
        
        return ''
    
    def _should_trigger_completion(self, key, text: str) -> bool:
        """
        Determine if completion should be triggered.
        
        Args:
            key: Input key
            text: Text representation
            
        Returns:
            True if should trigger completion
        """
        # Trigger on specific keys
        if key == Key.space and 'space' in self.trigger_keys:
            return True
        elif key == Key.enter and 'enter' in self.trigger_keys:
            return True
        elif key == Key.tab and 'tab' in self.trigger_keys:
            return True
        
        return False
    
    def enable_capture(self) -> None:
        """Enable text capture."""
        self.capture_enabled = True
    
    def disable_capture(self) -> None:
        """Disable text capture."""
        self.capture_enabled = False
    
    def is_typing_active(self) -> bool:
        """
        Check if user is actively typing.
        
        Returns:
            True if typing session is active
        """
        if not self.typing_session_active:
            return False
        
        # Check if idle for too long
        if time.time() - self.last_key_time > self.idle_threshold:
            self.typing_session_active = False
            return False
        
        return True
    
    def get_status(self) -> dict:
        """
        Get monitor status.
        
        Returns:
            Status dictionary
        """
        return {
            'running': self.running,
            'enabled': self.enabled,
            'capture_enabled': self.capture_enabled,
            'typing_active': self.is_typing_active(),
            'last_key_time': self.last_key_time,
            'hotkeys_configured': len(self.hotkey_combinations)
        }
