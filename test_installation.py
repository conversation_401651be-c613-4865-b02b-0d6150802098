#!/usr/bin/env python3
"""
Test script to verify Quill installation and dependencies.
"""

import sys
import importlib
from pathlib import Path

def test_python_version():
    """Test Python version compatibility."""
    print("🐍 Testing Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def test_dependencies():
    """Test required dependencies."""
    print("\n📦 Testing dependencies...")
    
    dependencies = [
        ("PyQt5", "PyQt5"),
        ("requests", "requests"),
        ("yaml", "yaml"),
        ("pynput", "pynput"),
    ]
    
    all_good = True
    
    for name, module in dependencies:
        try:
            importlib.import_module(module)
            print(f"✅ {name} - Available")
        except ImportError:
            print(f"❌ {name} - Missing (install with: pip install {name.lower()})")
            all_good = False
    
    return all_good

def test_project_structure():
    """Test project structure."""
    print("\n📁 Testing project structure...")
    
    required_paths = [
        "src/",
        "src/core/",
        "src/ui/",
        "src/utils/",
        "src/workers/",
        "config/",
        "config/settings.yaml",
        "requirements.txt",
    ]
    
    all_good = True
    project_root = Path(__file__).parent
    
    for path_str in required_paths:
        path = project_root / path_str
        if path.exists():
            print(f"✅ {path_str} - Found")
        else:
            print(f"❌ {path_str} - Missing")
            all_good = False
    
    return all_good

def test_ollama_connection():
    """Test Ollama connection."""
    print("\n🤖 Testing Ollama connection...")
    
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama - Running and accessible")
            
            # Check for the required model
            data = response.json()
            models = [model['name'] for model in data.get('models', [])]
            target_model = "hf.co/unsloth/Qwen3-0.6B-GGUF:Q4_K_M"
            
            if target_model in models:
                print(f"✅ Model {target_model} - Available")
            else:
                print(f"⚠️  Model {target_model} - Not found")
                print(f"   Install with: ollama pull {target_model}")
            
            return True
        else:
            print(f"❌ Ollama - HTTP {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Ollama - Not running or not accessible")
        print("   Start Ollama and try again")
        return False
    except Exception as e:
        print(f"❌ Ollama - Error: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("\n⚙️  Testing configuration...")
    
    try:
        # Add src to path
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        
        from utils.config import config
        
        # Test basic config access
        app_name = config.get('app.name', 'Unknown')
        llm_model = config.get('llm.model', 'Unknown')
        
        print(f"✅ Configuration loaded successfully")
        print(f"   App: {app_name}")
        print(f"   Model: {llm_model}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration - Error: {e}")
        return False

def main():
    """Run all tests."""
    print("🪶 Quill AI Writing Assistant - Installation Test\n")
    
    tests = [
        ("Python Version", test_python_version),
        ("Dependencies", test_dependencies),
        ("Project Structure", test_project_structure),
        ("Ollama Connection", test_ollama_connection),
        ("Configuration", test_configuration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} - Exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 Test Summary:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Quill is ready to run.")
        print("   Start with: python src/main.py")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
