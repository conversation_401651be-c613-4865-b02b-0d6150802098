#!/usr/bin/env python3
"""
Legacy app.py - Backward compatibility wrapper
The new modular structure is in the src/ directory

This file provides backward compatibility for users who expect to run 'python app.py'
The actual application code has been completely rewritten and modularized in src/
"""

import sys
import os

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from main import main

    if __name__ == '__main__':
        print("🪶 Quill AI Writing Assistant v2.0")
        print("Starting with new modular architecture...")
        print("Note: Use 'python src/main.py' or 'python run_quill.py' for direct access")
        print()
        main()

except ImportError as e:
    print(f"❌ Error importing new modules: {e}")
    print("Please install dependencies: pip install -r requirements.txt")
    print("Or run the installation test: python test_installation.py")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error starting Quill: {e}")
    sys.exit(1)