#!/usr/bin/env python3
"""
Simple startup script for Quill AI Writing Assistant.
"""

import sys
import os
from pathlib import Path

def main():
    """Run Quill with proper path setup."""
    # Get the project root directory
    project_root = Path(__file__).parent
    
    # Add src directory to Python path
    src_path = project_root / "src"
    sys.path.insert(0, str(src_path))
    
    try:
        # Import and run the main application
        from main import main as quill_main
        return quill_main()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Make sure all dependencies are installed:")
        print("pip install -r requirements.txt")
        return 1
        
    except Exception as e:
        print(f"❌ Error starting Quill: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
