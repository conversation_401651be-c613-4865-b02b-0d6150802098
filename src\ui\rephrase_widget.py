"""
Rephrase widget for text modification with instructions.
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, 
                            QPushButton, QLabel)
from PyQt5.QtCore import Qt, pyqtSignal

from .styles import styles
from ..utils.clipboard import ClipboardManager


class RephraseWidget(QWidget):
    """Widget for rephrasing selected text with custom instructions."""
    
    # Signals
    rephrase_completed = pyqtSignal(str)
    widget_closed = pyqtSignal()
    
    def __init__(self, parent=None):
        """
        Initialize rephrase widget.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Widget configuration
        self.setWindowFlags(Qt.ToolTip | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Clipboard manager
        self.clipboard_manager = ClipboardManager()
        
        # State
        self.original_text = ""
        self.is_processing = False
        
        # Setup UI
        self._setup_ui()
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # Header
        self._create_header(layout)
        
        # Original text display
        self._create_original_text_display(layout)
        
        # Instructions input
        self._create_instructions_input(layout)
        
        # Action buttons
        self._create_action_buttons(layout)
        
        # Apply styles
        self.setStyleSheet(styles.floating_widget)
        
        # Set size
        self.setFixedSize(400, 350)
        
        # Initially hidden
        self.hide()
    
    def _create_header(self, layout: QVBoxLayout) -> None:
        """Create the header with title and close button."""
        header = QHBoxLayout()
        
        # Title
        title = QLabel("🔄 Rephrase Text")
        title.setStyleSheet(styles.title_label)
        header.addWidget(title)
        
        # Close button
        close_btn = QPushButton("×")
        close_btn.setFixedSize(25, 25)
        close_btn.clicked.connect(self.hide)
        close_btn.setStyleSheet(styles.close_button)
        header.addWidget(close_btn)
        
        layout.addLayout(header)
    
    def _create_original_text_display(self, layout: QVBoxLayout) -> None:
        """Create the original text display."""
        # Label
        label = QLabel("Original Text:")
        label.setStyleSheet(styles.label)
        layout.addWidget(label)
        
        # Text display
        self.original_text_display = QTextEdit()
        self.original_text_display.setReadOnly(True)
        self.original_text_display.setMaximumHeight(80)
        self.original_text_display.setStyleSheet(f"""
            {styles.text_edit}
            QTextEdit {{
                background-color: rgba(60, 65, 75, 0.95);
                color: #cccccc;
            }}
        """)
        layout.addWidget(self.original_text_display)
    
    def _create_instructions_input(self, layout: QVBoxLayout) -> None:
        """Create the instructions input."""
        # Label
        label = QLabel("Instructions (optional):")
        label.setStyleSheet(styles.label)
        layout.addWidget(label)
        
        # Instructions input
        self.instructions_input = QTextEdit()
        self.instructions_input.setPlaceholderText(
            "How would you like to rephrase this text?\n"
            "Examples:\n"
            "- Make it more formal\n"
            "- Simplify the language\n"
            "- Make it more engaging\n"
            "- Fix grammar and spelling"
        )
        self.instructions_input.setMaximumHeight(100)
        self.instructions_input.setStyleSheet(styles.text_edit)
        layout.addWidget(self.instructions_input)
    
    def _create_action_buttons(self, layout: QVBoxLayout) -> None:
        """Create the action buttons."""
        buttons_layout = QHBoxLayout()
        
        # Cancel button
        cancel_btn = QPushButton("Cancel")
        cancel_btn.clicked.connect(self.hide)
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {styles.muted};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {styles._darken_color(styles.muted, 0.1)};
            }}
        """)
        buttons_layout.addWidget(cancel_btn)
        
        # Rephrase button
        self.rephrase_btn = QPushButton("🔄 Rephrase")
        self.rephrase_btn.clicked.connect(self._rephrase_text)
        self.rephrase_btn.setStyleSheet(styles.primary_button)
        buttons_layout.addWidget(self.rephrase_btn)
        
        layout.addLayout(buttons_layout)
    
    def set_text(self, text: str) -> None:
        """
        Set the text to be rephrased.
        
        Args:
            text: Original text
        """
        self.original_text = text
        self.original_text_display.setPlainText(text)
        
        # Clear previous instructions
        self.instructions_input.clear()
        
        # Focus on instructions input
        self.instructions_input.setFocus()
    
    def _rephrase_text(self) -> None:
        """Initiate text rephrasing."""
        if self.is_processing or not self.original_text:
            return
        
        # Get instructions
        instructions = self.instructions_input.toPlainText().strip()
        
        # Update UI state
        self.is_processing = True
        self.rephrase_btn.setText("Processing...")
        self.rephrase_btn.setEnabled(False)
        
        # Get assistant from parent
        if hasattr(self.parent(), 'get_assistant'):
            assistant = self.parent().get_assistant()
            
            # Request rephrasing
            request_id = assistant.rephrase_text(self.original_text, instructions)
            
            if request_id:
                # Connect to completion signal
                assistant.generation_completed.connect(self._handle_rephrase_completed)
                assistant.generation_failed.connect(self._handle_rephrase_failed)
            else:
                self._handle_rephrase_failed("", "Failed to queue rephrase request")
        else:
            self._handle_rephrase_failed("", "Assistant not available")
    
    def _handle_rephrase_completed(self) -> None:
        """Handle rephrase completion."""
        # This is a simplified version - in a full implementation,
        # you'd need to track request IDs to match responses
        self._reset_ui_state()
        
        # The actual rephrased text would be handled through the generation worker
        # For now, we'll simulate success
        self.rephrase_completed.emit("Rephrase completed")
        self.hide()
    
    def _handle_rephrase_failed(self, request_id: str, error: str) -> None:
        """
        Handle rephrase failure.
        
        Args:
            request_id: Request identifier
            error: Error message
        """
        self._reset_ui_state()
        
        # Show error (in a real implementation, you'd show this in a status label)
        print(f"Rephrase failed: {error}")
    
    def _reset_ui_state(self) -> None:
        """Reset UI to normal state."""
        self.is_processing = False
        self.rephrase_btn.setText("🔄 Rephrase")
        self.rephrase_btn.setEnabled(True)
    
    def keyPressEvent(self, event) -> None:
        """Handle key press events."""
        if event.key() == Qt.Key_Escape:
            self.hide()
        elif event.key() == Qt.Key_Return and event.modifiers() == Qt.ControlModifier:
            # Ctrl+Enter to rephrase
            self._rephrase_text()
        else:
            super().keyPressEvent(event)
    
    def showEvent(self, event) -> None:
        """Handle show event."""
        super().showEvent(event)
        
        # Set focus to instructions input
        self.instructions_input.setFocus()
        
        # Ensure widget is on top
        self.raise_()
        self.activateWindow()
    
    def hideEvent(self, event) -> None:
        """Handle hide event."""
        super().hideEvent(event)
        
        # Reset state
        self._reset_ui_state()
        
        # Emit closed signal
        self.widget_closed.emit()
    
    def show_at_position(self, x: int, y: int) -> None:
        """
        Show widget at specific position.
        
        Args:
            x: X coordinate
            y: Y coordinate
        """
        self.move(x, y)
        self.show()
    
    def get_original_text(self) -> str:
        """
        Get the original text.
        
        Returns:
            Original text
        """
        return self.original_text
    
    def get_instructions(self) -> str:
        """
        Get the rephrase instructions.
        
        Returns:
            Instructions text
        """
        return self.instructions_input.toPlainText().strip()
    
    def clear(self) -> None:
        """Clear all text fields."""
        self.original_text = ""
        self.original_text_display.clear()
        self.instructions_input.clear()
        self._reset_ui_state()
