"""
Suggestion widget for displaying text completions.
"""

from PyQt5.QtWidgets import <PERSON>Widget, QVBoxLayout, QListWidget, QListWidgetItem
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import Q<PERSON><PERSON>Sequence
from typing import List

from .styles import styles
from ..utils.clipboard import ClipboardManager


class SuggestionWidget(QWidget):
    """Widget for displaying and selecting text suggestions."""
    
    # Signals
    suggestion_selected = pyqtSignal(str)
    widget_closed = pyqtSignal()
    
    def __init__(self, parent=None):
        """
        Initialize suggestion widget.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Widget configuration
        self.setWindowFlags(Qt.ToolTip | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFocusPolicy(Qt.StrongFocus)
        
        # Clipboard manager
        self.clipboard_manager = ClipboardManager()
        
        # Setup UI
        self._setup_ui()
        
        # State
        self.suggestions = []
        self.current_selection = 0
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Suggestions list
        self.suggestions_list = QListWidget()
        self.suggestions_list.setMaximumHeight(200)
        self.suggestions_list.setMinimumWidth(300)
        self.suggestions_list.itemClicked.connect(self._on_suggestion_clicked)
        self.suggestions_list.itemActivated.connect(self._on_suggestion_activated)
        
        # Apply styles
        self.setStyleSheet(styles.floating_widget)
        self.suggestions_list.setStyleSheet(styles.list_widget)
        
        layout.addWidget(self.suggestions_list)
        
        # Initially hidden
        self.hide()
    
    def set_suggestions(self, suggestions: List[str]) -> None:
        """
        Set the list of suggestions to display.
        
        Args:
            suggestions: List of suggestion strings
        """
        self.suggestions = suggestions
        self.current_selection = 0
        
        # Clear existing items
        self.suggestions_list.clear()
        
        # Add new suggestions
        for i, suggestion in enumerate(suggestions):
            item = QListWidgetItem(suggestion)
            item.setData(Qt.UserRole, i)
            self.suggestions_list.addItem(item)
        
        # Select first item
        if suggestions:
            self.suggestions_list.setCurrentRow(0)
            self._update_size()
    
    def _update_size(self) -> None:
        """Update widget size based on content."""
        if not self.suggestions:
            return
        
        # Calculate optimal size
        item_height = 40  # Approximate height per item
        max_height = min(len(self.suggestions) * item_height + 20, 200)
        
        # Calculate width based on longest suggestion
        max_width = 300
        if self.suggestions:
            font_metrics = self.fontMetrics()
            for suggestion in self.suggestions:
                text_width = font_metrics.boundingRect(suggestion).width() + 40
                max_width = max(max_width, min(text_width, 500))
        
        self.setFixedSize(max_width, max_height)
        self.suggestions_list.setFixedSize(max_width - 10, max_height - 10)
    
    def _on_suggestion_clicked(self, item: QListWidgetItem) -> None:
        """
        Handle suggestion item click.
        
        Args:
            item: Clicked list item
        """
        suggestion = item.text()
        self._use_suggestion(suggestion)
    
    def _on_suggestion_activated(self, item: QListWidgetItem) -> None:
        """
        Handle suggestion item activation (double-click or Enter).
        
        Args:
            item: Activated list item
        """
        suggestion = item.text()
        self._use_suggestion(suggestion)
    
    def _use_suggestion(self, suggestion: str) -> None:
        """
        Use the selected suggestion.
        
        Args:
            suggestion: Selected suggestion text
        """
        if suggestion:
            # Copy to clipboard and paste
            self.clipboard_manager.insert_text(suggestion)
            
            # Emit signal
            self.suggestion_selected.emit(suggestion)
            
            # Hide widget
            self.hide()
    
    def keyPressEvent(self, event) -> None:
        """Handle key press events."""
        key = event.key()
        
        if key == Qt.Key_Escape:
            self.hide()
        elif key == Qt.Key_Return or key == Qt.Key_Enter:
            # Use current selection
            current_item = self.suggestions_list.currentItem()
            if current_item:
                self._use_suggestion(current_item.text())
        elif key == Qt.Key_Up:
            # Move selection up
            current_row = self.suggestions_list.currentRow()
            if current_row > 0:
                self.suggestions_list.setCurrentRow(current_row - 1)
        elif key == Qt.Key_Down:
            # Move selection down
            current_row = self.suggestions_list.currentRow()
            if current_row < self.suggestions_list.count() - 1:
                self.suggestions_list.setCurrentRow(current_row + 1)
        elif key == Qt.Key_Tab:
            # Use current selection
            current_item = self.suggestions_list.currentItem()
            if current_item:
                self._use_suggestion(current_item.text())
        else:
            # Handle number keys for quick selection
            if Qt.Key_1 <= key <= Qt.Key_9:
                index = key - Qt.Key_1
                if 0 <= index < self.suggestions_list.count():
                    item = self.suggestions_list.item(index)
                    self._use_suggestion(item.text())
            else:
                super().keyPressEvent(event)
    
    def focusOutEvent(self, event) -> None:
        """Handle focus out event."""
        # Hide widget when it loses focus
        self.hide()
        super().focusOutEvent(event)
    
    def showEvent(self, event) -> None:
        """Handle show event."""
        super().showEvent(event)
        
        # Set focus to enable keyboard navigation
        self.setFocus()
        self.suggestions_list.setFocus()
        
        # Ensure widget is on top
        self.raise_()
        self.activateWindow()
    
    def hideEvent(self, event) -> None:
        """Handle hide event."""
        super().hideEvent(event)
        self.widget_closed.emit()
    
    def show_at_position(self, x: int, y: int) -> None:
        """
        Show widget at specific position.
        
        Args:
            x: X coordinate
            y: Y coordinate
        """
        self.move(x, y)
        self.show()
    
    def add_suggestion(self, suggestion: str) -> None:
        """
        Add a single suggestion to the list.
        
        Args:
            suggestion: Suggestion text to add
        """
        if suggestion not in self.suggestions:
            self.suggestions.append(suggestion)
            
            item = QListWidgetItem(suggestion)
            item.setData(Qt.UserRole, len(self.suggestions) - 1)
            self.suggestions_list.addItem(item)
            
            self._update_size()
    
    def clear_suggestions(self) -> None:
        """Clear all suggestions."""
        self.suggestions.clear()
        self.suggestions_list.clear()
        self.current_selection = 0
    
    def get_current_suggestion(self) -> str:
        """
        Get the currently selected suggestion.
        
        Returns:
            Current suggestion text or empty string
        """
        current_item = self.suggestions_list.currentItem()
        return current_item.text() if current_item else ""
    
    def select_suggestion(self, index: int) -> bool:
        """
        Select suggestion by index.
        
        Args:
            index: Index of suggestion to select
            
        Returns:
            True if selection was successful
        """
        if 0 <= index < self.suggestions_list.count():
            self.suggestions_list.setCurrentRow(index)
            self.current_selection = index
            return True
        return False
    
    def get_suggestion_count(self) -> int:
        """
        Get the number of suggestions.
        
        Returns:
            Number of suggestions
        """
        return len(self.suggestions)
    
    def is_empty(self) -> bool:
        """
        Check if widget has no suggestions.
        
        Returns:
            True if no suggestions
        """
        return len(self.suggestions) == 0
