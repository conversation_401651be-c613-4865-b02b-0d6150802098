# 🪶 Quill - AI Writing Assistant v2.0

**Production-grade, local AI writing assistant for enhanced productivity anywhere on your screen.**

Quill is a completely rewritten, high-performance AI writing assistant that runs entirely on your local machine. Built with a modern, modular architecture for blazing-fast performance and reliability.

## ✨ Key Features

### 🚀 **Blazing Fast Performance**
- **Optimized LLM Integration**: Direct Ollama API with connection pooling and caching
- **Smart Text Monitoring**: Intelligent context capture with minimal system impact
- **Async Operations**: Non-blocking UI with background processing
- **Memory Efficient**: Optimized buffer management and resource cleanup

### 🎯 **Intelligent Writing Assistance**
- **Context-Aware Completions**: Smart text completion based on typing patterns
- **Auto-Write**: Generate content from natural language prompts
- **Advanced Rephrasing**: Rephrase text with custom instructions
- **Real-time Suggestions**: Instant suggestions as you type

### 🛡️ **Privacy & Security**
- **100% Local Processing**: All AI operations happen on your machine
- **No Data Collection**: Your text never leaves your computer
- **Secure**: No internet required after initial model download

### ⚡ **Enhanced User Experience**
- **Global Hotkeys**: Quick access from anywhere
- **System Tray Integration**: Runs quietly in the background
- **Floating Interface**: Always available, never intrusive
- **Customizable**: Extensive configuration options

## 🏗️ Architecture

```
quill/
├── src/
│   ├── core/           # Core functionality
│   │   ├── assistant.py      # Main coordinator
│   │   ├── llm_manager.py    # Optimized LLM handling
│   │   ├── text_buffer.py    # Smart text capture
│   │   └── keyboard_monitor.py # Efficient monitoring
│   ├── ui/             # User interface
│   │   ├── main_window.py    # Main floating window
│   │   ├── suggestion_widget.py
│   │   ├── rephrase_widget.py
│   │   └── styles.py         # Centralized styling
│   ├── utils/          # Utilities
│   │   ├── config.py         # Configuration management
│   │   └── clipboard.py      # Clipboard operations
│   └── workers/        # Background processing
│       └── generation_worker.py # Async text generation
├── config/
│   └── settings.yaml   # Configuration file
└── requirements.txt
```

## 🚀 Quick Start

### Prerequisites
- **Python 3.8+**
- **Ollama** ([Download here](https://ollama.ai/))

### Installation

1. **Clone the repository:**
```bash
git clone https://github.com/your-username/quill.git
cd quill
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Install the optimized model:**
```bash
ollama pull hf.co/unsloth/Qwen3-0.6B-GGUF:Q4_K_M
```

4. **Run Quill:**
```bash
python src/main.py
```

### Alternative Installation
```bash
pip install -e .
quill
```

## 💡 Usage

### **Auto-Complete**
- Type naturally in any application
- Suggestions appear automatically after trigger keys (space, enter, tab)
- Use arrow keys to navigate, Enter/Tab to accept

### **Auto-Write**
- Click "✨ Auto Write" or use hotkey `Ctrl+Shift+Q`
- Describe what you want to write
- Generated content is automatically inserted

### **Rephrase**
- Select text in any application
- Click "🔄 Rephrase" or use hotkey `Ctrl+Shift+R`
- Add optional instructions for specific style changes

### **Global Hotkeys**
- `Ctrl+Shift+Q`: Toggle assistant window
- `Ctrl+Shift+C`: Quick completion
- `Ctrl+Shift+R`: Rephrase selected text

## ⚙️ Configuration

Edit `config/settings.yaml` to customize:

```yaml
# LLM Configuration
llm:
  model: "hf.co/unsloth/Qwen3-0.6B-GGUF:Q4_K_M"
  temperature: 0.7
  max_tokens: 512

# Performance
performance:
  cache_size: 100
  max_concurrent_requests: 3

# UI Customization
ui:
  theme: "dark"
  opacity: 0.95
  colors:
    primary: "#4a9eff"
    secondary: "#45a165"
```

## 🔧 Advanced Features

### **Performance Optimizations**
- **LLM Response Caching**: Avoid redundant API calls
- **Smart Context Management**: Optimal context window usage
- **Debounced Input**: Prevents excessive processing
- **Connection Pooling**: Reuse HTTP connections

### **Intelligent Text Processing**
- **Context-Aware Triggers**: Smart completion timing
- **Sentence Boundary Detection**: Natural break points
- **Typing Pattern Analysis**: Adaptive to user behavior

## 🛠️ Development

### **Project Structure**
- **Modular Design**: Clean separation of concerns
- **Type Hints**: Full type annotation for better IDE support
- **Error Handling**: Comprehensive error management
- **Logging**: Detailed logging for debugging

### **Key Components**
- **LLMManager**: Handles all AI model interactions
- **TextBuffer**: Intelligent text capture and context management
- **GenerationWorker**: Async text generation with queue management
- **KeyboardMonitor**: Efficient global keyboard monitoring

## 🔒 Privacy & Security

- **Local Processing**: All AI operations happen on your machine
- **No Telemetry**: No usage data is collected or transmitted
- **Secure Storage**: Configuration stored locally
- **Memory Safety**: Automatic cleanup of sensitive data

## 📊 Performance

### **Benchmarks** (on typical hardware)
- **Startup Time**: < 2 seconds
- **Response Time**: 200-800ms (depending on model and prompt)
- **Memory Usage**: ~50-100MB
- **CPU Impact**: Minimal when idle

### **Optimizations**
- **Model**: Qwen3-0.6B optimized for speed and quality
- **Quantization**: Q4_K_M for optimal size/performance balance
- **Caching**: Intelligent response caching
- **Threading**: Non-blocking operations

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

Licensed under the Apache License 2.0. See [LICENSE](LICENSE) for details.

## 🙏 Acknowledgments

- **Ollama Team**: For the excellent local LLM runtime
- **Unsloth**: For the optimized Qwen3 model
- **PyQt5**: For the robust GUI framework

---

**Made with ❤️ for enhanced writing productivity**

*Quill v2.0 - Completely rewritten for production use*
