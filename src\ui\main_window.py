"""
Main floating window for Quill AI Writing Assistant.
"""

from PyQt5.QtWidgets import (QMain<PERSON>indow, QWidget, QPushButton, QVBoxLayout, 
                            QHBoxLayout, QLabel, QApplication)
from PyQt5.QtCore import Qt, QPoint, pyqtSlot
from PyQt5.QtGui import QCursor

from .styles import styles
from .suggestion_widget import SuggestionWidget
from .rephrase_widget import RephraseWidget
from .auto_write_dialog import AutoWriteDialog
from ..core.assistant import WritingAssistant
from ..utils.config import config


class MainWindow(QMainWindow):
    """Main floating window for the writing assistant."""
    
    def __init__(self):
        """Initialize the main window."""
        super().__init__()
        
        # Configuration
        self.window_config = config.get('ui.window_size', {'width': 400, 'height': 300})
        self.position_config = config.get('ui.position', {'x': 100, 'y': 100})
        
        # Initialize assistant
        self.assistant = WritingAssistant()
        
        # Initialize UI components
        self.suggestion_widget = None
        self.rephrase_widget = None
        self.auto_write_dialog = None
        
        # Window state
        self.old_pos = None
        
        self._setup_window()
        self._setup_ui()
        self._connect_signals()
        
        # Initialize child widgets
        self._init_child_widgets()
    
    def _setup_window(self) -> None:
        """Setup window properties."""
        self.setWindowTitle(config.get('app.window_title', '✏️ Writing Assistant'))
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Set size and position
        self.setMinimumSize(
            self.window_config['width'], 
            self.window_config['height']
        )
        self.move(
            self.position_config['x'], 
            self.position_config['y']
        )
        
        # Apply main window style
        self.setStyleSheet(styles.main_window)
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        self._create_header(layout)
        
        # Spacing
        layout.addSpacing(10)
        
        # Main action button
        self._create_main_button(layout)
        
        # Feature buttons
        self._create_feature_buttons(layout)
        
        # Status indicator
        self._create_status_indicator(layout)
    
    def _create_header(self, layout: QVBoxLayout) -> None:
        """Create the header with title and close button."""
        header = QHBoxLayout()
        
        # Title
        title = QLabel(config.get('app.window_title', '✏️ Writing Assistant'))
        title.setStyleSheet(styles.title_label)
        header.addWidget(title)
        
        # Close button
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet(styles.close_button)
        header.addWidget(close_btn)
        
        layout.addLayout(header)
    
    def _create_main_button(self, layout: QVBoxLayout) -> None:
        """Create the main auto-write button."""
        auto_write_btn = QPushButton("✨ Auto Write")
        auto_write_btn.setFixedHeight(50)
        auto_write_btn.clicked.connect(self._show_auto_write_dialog)
        auto_write_btn.setStyleSheet(styles.primary_button)
        layout.addWidget(auto_write_btn)
    
    def _create_feature_buttons(self, layout: QVBoxLayout) -> None:
        """Create the feature buttons container."""
        features_container = QWidget()
        features_layout = QHBoxLayout(features_container)
        features_layout.setSpacing(10)
        
        # Rephrase button
        rephrase_btn = QPushButton("🔄 Rephrase")
        rephrase_btn.setFixedHeight(50)
        rephrase_btn.clicked.connect(self._show_rephrase_dialog)
        rephrase_btn.setStyleSheet(styles.secondary_button)
        features_layout.addWidget(rephrase_btn)
        
        # Complete button
        complete_btn = QPushButton("✨ Complete")
        complete_btn.setFixedHeight(50)
        complete_btn.clicked.connect(self._trigger_completion)
        complete_btn.setStyleSheet(styles.accent_button)
        features_layout.addWidget(complete_btn)
        
        layout.addWidget(features_container)
    
    def _create_status_indicator(self, layout: QVBoxLayout) -> None:
        """Create the status indicator."""
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet(styles.status_label)
        layout.addWidget(self.status_label)
    
    def _init_child_widgets(self) -> None:
        """Initialize child widgets."""
        self.suggestion_widget = SuggestionWidget(self)
        self.rephrase_widget = RephraseWidget(self)
        self.auto_write_dialog = AutoWriteDialog(self)
    
    def _connect_signals(self) -> None:
        """Connect signals from the assistant."""
        self.assistant.suggestion_ready.connect(self._show_suggestion)
        self.assistant.status_changed.connect(self._update_status)
        self.assistant.error_occurred.connect(self._handle_error)
    
    @pyqtSlot(str)
    def _show_suggestion(self, suggestion: str) -> None:
        """
        Show completion suggestion.
        
        Args:
            suggestion: Generated suggestion text
        """
        if self.suggestion_widget and suggestion.strip():
            self.suggestion_widget.set_suggestions([suggestion])
            
            # Position near cursor
            cursor_pos = QCursor.pos()
            self.suggestion_widget.move(cursor_pos.x() + 10, cursor_pos.y() + 10)
            self.suggestion_widget.show()
    
    @pyqtSlot(str)
    def _update_status(self, status: str) -> None:
        """
        Update status indicator.
        
        Args:
            status: Status message
        """
        self.status_label.setText(status)
    
    @pyqtSlot(str)
    def _handle_error(self, error: str) -> None:
        """
        Handle error messages.
        
        Args:
            error: Error message
        """
        self.status_label.setText(f"Error: {error}")
        print(f"Assistant error: {error}")
    
    def _show_auto_write_dialog(self) -> None:
        """Show the auto-write dialog."""
        if self.auto_write_dialog:
            # Position dialog relative to main window
            dialog_pos = self.pos() + QPoint(
                (self.width() - self.auto_write_dialog.width()) // 2,
                (self.height() - self.auto_write_dialog.height()) // 2
            )
            self.auto_write_dialog.move(dialog_pos)
            self.auto_write_dialog.show()
    
    def _show_rephrase_dialog(self) -> None:
        """Show the rephrase dialog."""
        # Check if text is selected in clipboard
        clipboard = QApplication.clipboard()
        selected_text = clipboard.text(mode=clipboard.Selection)
        
        if selected_text and self.rephrase_widget:
            cursor_pos = QCursor.pos()
            self.rephrase_widget.move(cursor_pos.x() + 10, cursor_pos.y() + 10)
            self.rephrase_widget.set_text(selected_text)
            self.rephrase_widget.show()
        else:
            self.status_label.setText("No text selected for rephrasing")
    
    def _trigger_completion(self) -> None:
        """Trigger manual completion."""
        self.assistant.trigger_manual_completion()
        self.status_label.setText("Generating completion...")
    
    def mousePressEvent(self, event) -> None:
        """Handle mouse press for window dragging."""
        if event.button() == Qt.LeftButton:
            self.old_pos = event.globalPos()
    
    def mouseMoveEvent(self, event) -> None:
        """Handle mouse move for window dragging."""
        if event.buttons() == Qt.LeftButton and self.old_pos:
            delta = QPoint(event.globalPos() - self.old_pos)
            self.move(self.x() + delta.x(), self.y() + delta.y())
            self.old_pos = event.globalPos()
    
    def closeEvent(self, event) -> None:
        """Handle window close event."""
        # Save window position
        config.set('ui.position.x', self.x())
        config.set('ui.position.y', self.y())
        
        # Cleanup assistant
        if self.assistant:
            self.assistant.cleanup()
        
        # Close child widgets
        if self.suggestion_widget:
            self.suggestion_widget.close()
        if self.rephrase_widget:
            self.rephrase_widget.close()
        if self.auto_write_dialog:
            self.auto_write_dialog.close()
        
        event.accept()
    
    def show_at_cursor(self) -> None:
        """Show window near cursor position."""
        cursor_pos = QCursor.pos()
        self.move(cursor_pos.x() - self.width() // 2, cursor_pos.y() - self.height() // 2)
        self.show()
        self.raise_()
        self.activateWindow()
    
    def toggle_visibility(self) -> None:
        """Toggle window visibility."""
        if self.isVisible():
            self.hide()
        else:
            self.show_at_cursor()
    
    def get_assistant(self) -> 'WritingAssistant':
        """
        Get the writing assistant instance.
        
        Returns:
            WritingAssistant instance
        """
        return self.assistant
