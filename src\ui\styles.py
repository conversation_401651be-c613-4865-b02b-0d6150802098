"""
Premium Modern UI styles for Quill AI Writing Assistant.
Glassmorphism design with smooth animations and premium feel.
"""




class QuillStyles:
    """Premium style definitions with glassmorphism design."""

    def __init__(self):
        """Initialize premium styles."""
        # Premium Color palette - Dark theme with glassmorphism
        self.primary = '#6366f1'           # Indigo
        self.primary_hover = '#4f46e5'     # Darker indigo
        self.primary_light = '#a5b4fc'     # Light indigo
        self.accent = '#06b6d4'            # Cyan accent
        self.accent_hover = '#0891b2'      # Darker cyan

        # Status colors
        self.success = '#10b981'           # Emerald
        self.warning = '#f59e0b'           # Amber
        self.error = '#ef4444'             # Red
        self.info = '#3b82f6'              # Blue

        # Background colors (Dark theme with glass effect)
        self.bg_primary = '#0f172a'        # Slate 900
        self.bg_secondary = '#1e293b'      # Slate 800
        self.bg_tertiary = '#334155'       # Slate 700
        self.bg_glass = 'rgba(15, 23, 42, 0.85)'  # Glass effect
        self.bg_overlay = 'rgba(0, 0, 0, 0.6)'    # Modal overlay

        # Surface colors
        self.surface = '#1e293b'           # Slate 800
        self.surface_hover = '#334155'     # Slate 700
        self.surface_glass = 'rgba(30, 41, 59, 0.8)'  # Glass surface

        # Border colors
        self.border = '#334155'            # Slate 700
        self.border_light = '#475569'      # Slate 600
        self.border_focus = '#6366f1'      # Primary color

        # Text colors
        self.text_primary = '#f8fafc'      # Slate 50
        self.text_secondary = '#cbd5e1'    # Slate 300
        self.text_muted = '#94a3b8'        # Slate 400
        self.text_disabled = '#64748b'     # Slate 500

        # Legacy compatibility
        self.text = self.text_primary
        self.muted = self.text_muted
    
    @property
    def main_window(self) -> str:
        """Premium main window style."""
        return f"""
            QMainWindow {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.bg_primary}, stop:1 {self.bg_secondary});
                border-radius: 24px;
            }}
            QWidget {{
                background: {self.bg_glass};
                border-radius: 20px;
                border: 1px solid {self.border};
                color: {self.text_primary};
                font-family: 'Inter', 'Segoe UI', 'SF Pro Display', system-ui, sans-serif;
                font-weight: 400;
            }}
        """
    
    @property
    def floating_widget(self) -> str:
        """Premium floating widget."""
        return f"""
            QWidget {{
                background: {self.surface_glass};
                border-radius: 16px;
                border: 1px solid {self.border_light};
                color: {self.text_primary};
                font-family: 'Inter', 'Segoe UI', 'SF Pro Display', system-ui, sans-serif;
            }}
        """
    
    @property
    def primary_button(self) -> str:
        """Premium primary button with gradient."""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.primary}, stop:1 {self.primary_hover});
                color: white;
                border: none;
                border-radius: 12px;
                padding: 16px 24px;
                font-size: 15px;
                font-weight: 600;
                min-height: 20px;
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.primary_hover}, stop:1 {self.primary});
            }}
            QPushButton:pressed {{
                background: {self.primary_hover};
            }}
            QPushButton:disabled {{
                background: {self.text_disabled};
                color: {self.text_muted};
            }}
        """
    
    @property
    def secondary_button(self) -> str:
        """Premium secondary button with subtle styling."""
        return f"""
            QPushButton {{
                background: {self.surface};
                color: {self.text_primary};
                border: 1px solid {self.border_light};
                border-radius: 12px;
                padding: 16px 24px;
                font-size: 15px;
                font-weight: 500;
                min-height: 20px;
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            }}
            QPushButton:hover {{
                background: {self.surface_hover};
                border-color: {self.border_focus};
            }}
            QPushButton:pressed {{
                background: {self.bg_tertiary};
            }}
            QPushButton:disabled {{
                background: {self.bg_secondary};
                color: {self.text_disabled};
                border-color: {self.border};
            }}
        """
    
    @property
    def accent_button(self) -> str:
        """Premium accent button with cyan gradient."""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.accent}, stop:1 {self.accent_hover});
                color: white;
                border: none;
                border-radius: 12px;
                padding: 16px 24px;
                font-size: 15px;
                font-weight: 600;
                min-height: 20px;
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.accent_hover}, stop:1 {self.accent});
            }}
            QPushButton:pressed {{
                background: {self.accent_hover};
            }}
            QPushButton:disabled {{
                background: {self.text_disabled};
                color: {self.text_muted};
            }}
        """
    
    @property
    def close_button(self) -> str:
        """Premium close button with hover effect."""
        return f"""
            QPushButton {{
                background: transparent;
                color: {self.text_secondary};
                font-size: 16px;
                border: none;
                border-radius: 8px;
                padding: 8px;
                min-width: 32px;
                min-height: 32px;
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            }}
            QPushButton:hover {{
                background: {self.error};
                color: white;
            }}
            QPushButton:pressed {{
                background: #dc2626;
            }}
        """
    
    @property
    def text_edit(self) -> str:
        """Premium text edit."""
        return f"""
            QTextEdit {{
                background: {self.surface_glass};
                color: {self.text_primary};
                border: 1px solid {self.border};
                border-radius: 12px;
                padding: 16px;
                font-size: 15px;
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
                selection-background-color: {self.primary};
                selection-color: white;
            }}
            QTextEdit:focus {{
                border: 2px solid {self.border_focus};
                background: {self.surface};
            }}
            QTextEdit:hover {{
                border-color: {self.border_light};
            }}
        """
    
    @property
    def list_widget(self) -> str:
        """Premium list widget with smooth interactions."""
        return f"""
            QListWidget {{
                background: {self.surface_glass};
                color: {self.text_primary};
                border-radius: 12px;
                border: 1px solid {self.border};
                padding: 8px;
                outline: none;
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            }}
            QListWidget::item {{
                padding: 12px 16px;
                border-radius: 8px;
                margin: 2px;
                font-size: 14px;
                font-weight: 500;
            }}
            QListWidget::item:selected {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.primary}, stop:1 {self.primary_hover});
                color: white;
            }}
            QListWidget::item:hover {{
                background: {self.surface_hover};
            }}
        """
    
    @property
    def progress_bar(self) -> str:
        """Premium progress bar with gradient."""
        return f"""
            QProgressBar {{
                border: none;
                border-radius: 8px;
                text-align: center;
                background: {self.surface};
                color: {self.text_primary};
                font-weight: 600;
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
                height: 8px;
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.primary}, stop:1 {self.accent});
                border-radius: 8px;
            }}
        """
    
    @property
    def label(self) -> str:
        """Premium label style."""
        return f"""
            QLabel {{
                color: {self.text_primary};
                font-size: 14px;
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
                background: transparent;
                border: none;
                font-weight: 500;
            }}
        """

    @property
    def title_label(self) -> str:
        """Premium title label style."""
        return f"""
            QLabel {{
                color: {self.text_primary};
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
                font-size: 20px;
                font-weight: 700;
                padding: 12px;
                background: transparent;
                border: none;
                letter-spacing: -0.025em;
            }}
        """

    @property
    def status_label(self) -> str:
        """Premium status label."""
        return f"""
            QLabel {{
                color: {self.text_secondary};
                font-size: 13px;
                font-weight: 500;
                padding: 12px 16px;
                background: {self.surface_glass};
                border-radius: 12px;
                border: 1px solid {self.border};
                min-height: 20px;
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            }}
        """
    
    @property
    def loading_overlay(self) -> str:
        """Premium loading overlay."""
        return f"""
            QWidget {{
                background: {self.bg_overlay};
                border-radius: 20px;
            }}
            QLabel {{
                color: {self.text_primary};
                font-size: 15px;
                font-weight: 600;
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            }}
        """

    @property
    def success_button(self) -> str:
        """Premium success button."""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.success}, stop:1 #059669);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 16px 24px;
                font-size: 15px;
                font-weight: 600;
                min-height: 20px;
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #059669, stop:1 {self.success});
            }}
            QPushButton:pressed {{
                background: #059669;
            }}
        """

    @property
    def error_button(self) -> str:
        """Premium error button."""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {self.error}, stop:1 #dc2626);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 16px 24px;
                font-size: 15px;
                font-weight: 600;
                min-height: 20px;
                font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #dc2626, stop:1 {self.error});
            }}
            QPushButton:pressed {{
                background: #dc2626;
            }}
        """



# Global styles instance
styles = QuillStyles()
