"""
Centralized styling for Quill UI components.
"""

from ..utils.config import config


class QuillStyles:
    """Centralized style definitions for Quill UI."""
    
    def __init__(self):
        """Initialize styles with configuration."""
        self.colors = config.get('ui.colors', {})
        self.opacity = config.get('ui.opacity', 0.95)
        
        # Default colors if not configured
        self.primary = self.colors.get('primary', '#4a9eff')
        self.secondary = self.colors.get('secondary', '#45a165')
        self.accent = self.colors.get('accent', '#9b59b6')
        self.background = self.colors.get('background', 'rgba(40, 44, 52, 0.95)')
        self.text = self.colors.get('text', '#ffffff')
        self.border = self.colors.get('border', '#3d3d3d')
        
        # Additional colors
        self.error = '#ff4455'
        self.warning = '#ffa726'
        self.success = '#66bb6a'
        self.muted = '#8f9aab'
        self.dark_bg = 'rgba(55, 60, 70, 0.95)'
    
    @property
    def main_window(self) -> str:
        """Main window style."""
        return f"""
            QMainWindow {{
                background-color: transparent;
            }}
            QWidget {{
                background-color: {self.background};
                border-radius: 20px;
                border: 1px solid {self.border};
                color: {self.text};
                font-family: 'Segoe UI', Arial, sans-serif;
            }}
        """
    
    @property
    def floating_widget(self) -> str:
        """Floating widget base style."""
        return f"""
            QWidget {{
                background-color: {self.background};
                border-radius: 10px;
                border: 1px solid {self.border};
                color: {self.text};
                font-family: 'Segoe UI', Arial, sans-serif;
            }}
        """
    
    @property
    def primary_button(self) -> str:
        """Primary button style."""
        return f"""
            QPushButton {{
                background-color: {self.primary};
                color: white;
                border: none;
                border-radius: 12px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {self._darken_color(self.primary, 0.1)};
            }}
            QPushButton:pressed {{
                background-color: {self._darken_color(self.primary, 0.2)};
            }}
            QPushButton:disabled {{
                background-color: {self.muted};
                color: #cccccc;
            }}
        """
    
    @property
    def secondary_button(self) -> str:
        """Secondary button style."""
        return f"""
            QPushButton {{
                background-color: {self.secondary};
                color: white;
                border: none;
                border-radius: 12px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {self._darken_color(self.secondary, 0.1)};
            }}
            QPushButton:pressed {{
                background-color: {self._darken_color(self.secondary, 0.2)};
            }}
            QPushButton:disabled {{
                background-color: {self.muted};
                color: #cccccc;
            }}
        """
    
    @property
    def accent_button(self) -> str:
        """Accent button style."""
        return f"""
            QPushButton {{
                background-color: {self.accent};
                color: white;
                border: none;
                border-radius: 12px;
                padding: 15px;
                font-size: 16px;
                font-weight: bold;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {self._darken_color(self.accent, 0.1)};
            }}
            QPushButton:pressed {{
                background-color: {self._darken_color(self.accent, 0.2)};
            }}
            QPushButton:disabled {{
                background-color: {self.muted};
                color: #cccccc;
            }}
        """
    
    @property
    def close_button(self) -> str:
        """Close button style."""
        return f"""
            QPushButton {{
                background-color: transparent;
                color: {self.text};
                font-size: 18px;
                border: none;
                border-radius: 12px;
                padding: 5px;
                min-width: 25px;
                min-height: 25px;
            }}
            QPushButton:hover {{
                background-color: {self.error};
            }}
        """
    
    @property
    def text_edit(self) -> str:
        """Text edit style."""
        return f"""
            QTextEdit {{
                background-color: {self.dark_bg};
                color: {self.text};
                border: none;
                border-radius: 8px;
                padding: 12px;
                font-size: 14px;
                selection-background-color: {self.primary};
            }}
            QTextEdit:focus {{
                border: 2px solid {self.primary};
            }}
        """
    
    @property
    def list_widget(self) -> str:
        """List widget style."""
        return f"""
            QListWidget {{
                background-color: {self.dark_bg};
                color: {self.text};
                border-radius: 8px;
                border: 1px solid {self.border};
                padding: 5px;
                outline: none;
            }}
            QListWidget::item {{
                padding: 10px;
                border-radius: 6px;
                margin: 2px;
            }}
            QListWidget::item:selected {{
                background-color: {self.primary};
                color: white;
            }}
            QListWidget::item:hover {{
                background-color: {self._lighten_color(self.primary, 0.1)};
            }}
        """
    
    @property
    def progress_bar(self) -> str:
        """Progress bar style."""
        return f"""
            QProgressBar {{
                border: 2px solid {self.border};
                border-radius: 8px;
                text-align: center;
                background-color: {self.dark_bg};
                color: {self.text};
                font-weight: bold;
            }}
            QProgressBar::chunk {{
                background-color: {self.primary};
                border-radius: 6px;
                margin: 1px;
            }}
        """
    
    @property
    def label(self) -> str:
        """Label style."""
        return f"""
            QLabel {{
                color: {self.text};
                font-size: 14px;
                background-color: transparent;
                border: none;
            }}
        """
    
    @property
    def title_label(self) -> str:
        """Title label style."""
        return f"""
            QLabel {{
                color: {self.text};
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 18px;
                font-weight: bold;
                padding: 8px;
                background-color: transparent;
                border: none;
            }}
        """
    
    @property
    def status_label(self) -> str:
        """Status label style."""
        return f"""
            QLabel {{
                color: {self.muted};
                font-size: 14px;
                padding: 10px;
                background-color: {self.dark_bg};
                border-radius: 8px;
                min-height: 20px;
                border: none;
            }}
        """
    
    @property
    def loading_overlay(self) -> str:
        """Loading overlay style."""
        return f"""
            QWidget {{
                background-color: rgba(0, 0, 0, 180);
                border-radius: 20px;
            }}
            QLabel {{
                color: white;
                font-size: 14px;
                font-weight: bold;
            }}
        """
    
    def _darken_color(self, color: str, factor: float) -> str:
        """
        Darken a hex color by a factor.
        
        Args:
            color: Hex color string
            factor: Darkening factor (0.0 to 1.0)
            
        Returns:
            Darkened color
        """
        if not color.startswith('#'):
            return color
        
        try:
            # Remove # and convert to RGB
            hex_color = color[1:]
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)
            
            # Darken
            r = int(r * (1 - factor))
            g = int(g * (1 - factor))
            b = int(b * (1 - factor))
            
            # Convert back to hex
            return f"#{r:02x}{g:02x}{b:02x}"
            
        except (ValueError, IndexError):
            return color
    
    def _lighten_color(self, color: str, factor: float) -> str:
        """
        Lighten a hex color by a factor.
        
        Args:
            color: Hex color string
            factor: Lightening factor (0.0 to 1.0)
            
        Returns:
            Lightened color
        """
        if not color.startswith('#'):
            return color
        
        try:
            # Remove # and convert to RGB
            hex_color = color[1:]
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)
            
            # Lighten
            r = min(255, int(r + (255 - r) * factor))
            g = min(255, int(g + (255 - g) * factor))
            b = min(255, int(b + (255 - b) * factor))
            
            # Convert back to hex
            return f"#{r:02x}{g:02x}{b:02x}"
            
        except (ValueError, IndexError):
            return color


# Global styles instance
styles = QuillStyles()
