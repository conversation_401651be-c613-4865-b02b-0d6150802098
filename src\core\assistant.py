"""
Core writing assistant that coordinates all functionality.
"""

import uuid
from PyQt5.QtCore import QObject, pyqtSignal

from .llm_manager import llm_manager
from .text_buffer import TextBuffer
from .keyboard_monitor import KeyboardMonitor
from ..workers.generation_worker import GenerationWorker, GenerationRequest
from ..utils.config import config


class WritingAssistant(QObject):
    """
    Core writing assistant that coordinates text monitoring, 
    LLM generation, and user interactions.
    """
    
    # Signals
    suggestion_ready = pyqtSignal(str)
    status_changed = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    generation_started = pyqtSignal(str)  # request_id
    generation_completed = pyqtSignal(str, str)  # request_id, result
    generation_failed = pyqtSignal(str, str)  # request_id, error
    
    def __init__(self):
        """Initialize the writing assistant."""
        super().__init__()
        
        # Configuration
        self.enabled = True
        self.auto_completion_enabled = config.get('keyboard.enabled', True)
        
        # Initialize components
        self._init_text_buffer()
        self._init_keyboard_monitor()
        self._init_generation_worker()
        
        # State tracking
        self.last_suggestion_time = 0
        self.suggestion_cooldown = 2.0  # seconds
        self.active_requests = set()
        
        # Status
        self.current_status = "Ready"
        self._update_status("Initializing...")
        
        # Check LLM availability
        self._check_llm_availability()
    
    def _init_text_buffer(self) -> None:
        """Initialize the text buffer."""
        self.text_buffer = TextBuffer(
            on_context_ready=self._handle_context_ready
        )
    
    def _init_keyboard_monitor(self) -> None:
        """Initialize the keyboard monitor."""
        self.keyboard_monitor = KeyboardMonitor(self.text_buffer)
        self.keyboard_monitor.text_captured.connect(self._handle_text_captured)
        self.keyboard_monitor.hotkey_triggered.connect(self._handle_hotkey)
        
        # Start monitoring if enabled
        if self.auto_completion_enabled:
            self._start_monitoring()
    
    def _init_generation_worker(self) -> None:
        """Initialize the generation worker."""
        max_queue_size = config.get('performance.max_concurrent_requests', 3)
        self.generation_worker = GenerationWorker(max_queue_size)
        
        # Connect signals
        self.generation_worker.generation_completed.connect(self._handle_generation_completed)
        self.generation_worker.generation_failed.connect(self._handle_generation_failed)
        self.generation_worker.generation_started.connect(self._handle_generation_started)
        self.generation_worker.queue_status_changed.connect(self._handle_queue_status_changed)
        
        # Start worker
        self.generation_worker.start()
    
    def _check_llm_availability(self) -> None:
        """Check if LLM service is available."""
        if llm_manager.is_available():
            self._update_status("Ready")
        else:
            self._update_status("LLM service unavailable")
            self.error_occurred.emit("LLM service is not available. Please check Ollama.")
    
    def _start_monitoring(self) -> bool:
        """
        Start keyboard monitoring.
        
        Returns:
            True if started successfully
        """
        if self.keyboard_monitor.start():
            self._update_status("Monitoring active")
            return True
        else:
            self._update_status("Monitoring failed to start")
            self.error_occurred.emit("Failed to start keyboard monitoring")
            return False
    
    def _stop_monitoring(self) -> None:
        """Stop keyboard monitoring."""
        self.keyboard_monitor.stop()
        self._update_status("Monitoring stopped")
    
    def _handle_context_ready(self, context: str) -> None:
        """
        Handle when text context is ready for processing.
        
        Args:
            context: Text context from buffer
        """
        if not self.enabled or not context.strip():
            return
        
        # Check cooldown
        import time
        current_time = time.time()
        if current_time - self.last_suggestion_time < self.suggestion_cooldown:
            return
        
        self.last_suggestion_time = current_time
        
        # Generate completion
        self._generate_completion(context)
    
    def _handle_text_captured(self, text: str) -> None:
        """
        Handle captured text from keyboard monitor.
        
        Args:
            text: Captured text
        """
        # This is called when trigger keys are pressed
        # The text buffer already has the context
        if self.enabled and text.strip():
            self._generate_completion(text)
    
    def _handle_hotkey(self, action: str) -> None:
        """
        Handle hotkey triggers.
        
        Args:
            action: Hotkey action name
        """
        if action == "toggle_assistant":
            self.toggle_enabled()
        elif action == "quick_complete":
            self.trigger_manual_completion()
        elif action == "rephrase":
            # This would be handled by the main window
            pass
    
    def _generate_completion(self, context: str) -> None:
        """
        Generate text completion for given context.
        
        Args:
            context: Text context for completion
        """
        if not llm_manager.is_available():
            self.error_occurred.emit("LLM service is not available")
            return
        
        # Create generation request
        request_id = str(uuid.uuid4())
        request = GenerationRequest(
            request_id=request_id,
            prompt=context,
            generation_type="completion",
            priority=1
        )
        
        # Add to worker queue
        if self.generation_worker.add_request(request):
            self.active_requests.add(request_id)
            self._update_status("Generating completion...")
        else:
            self.error_occurred.emit("Generation queue is full")
    
    def _handle_generation_started(self, request_id: str) -> None:
        """
        Handle generation started.

        Args:
            request_id: Request identifier
        """
        if request_id in self.active_requests:
            self.generation_started.emit(request_id)
    
    def _handle_generation_completed(self, request_id: str, result: str) -> None:
        """
        Handle completed generation.

        Args:
            request_id: Request identifier
            result: Generated text
        """
        if request_id in self.active_requests:
            self.active_requests.discard(request_id)

            if result.strip():
                self.suggestion_ready.emit(result.strip())
                self._update_status("Suggestion ready")
            else:
                self._update_status("Empty response")

            self.generation_completed.emit(request_id, result)
    
    def _handle_generation_failed(self, request_id: str, error: str) -> None:
        """
        Handle failed generation.

        Args:
            request_id: Request identifier
            error: Error message
        """
        if request_id in self.active_requests:
            self.active_requests.discard(request_id)

            self.error_occurred.emit(f"Generation failed: {error}")
            self._update_status("Generation failed")

        # Always emit the generation_failed signal for UI components to handle
        self.generation_failed.emit(request_id, error)
    
    def _handle_queue_status_changed(self, queue_size: int) -> None:
        """
        Handle queue status change.
        
        Args:
            queue_size: Current queue size
        """
        if queue_size > 0:
            self._update_status(f"Queue: {queue_size} pending")
        elif not self.active_requests:
            self._update_status("Ready")
    
    def _update_status(self, status: str) -> None:
        """
        Update current status.
        
        Args:
            status: New status message
        """
        self.current_status = status
        self.status_changed.emit(status)
    
    def trigger_manual_completion(self) -> None:
        """Trigger manual completion based on current buffer."""
        context = self.text_buffer.get_context()
        if context:
            self._generate_completion(context)
        else:
            self._update_status("No context available")
    
    def generate_content(self, prompt: str) -> str:
        """
        Generate content based on prompt (synchronous).
        
        Args:
            prompt: Content prompt
            
        Returns:
            Generated content request ID
        """
        request_id = str(uuid.uuid4())
        request = GenerationRequest(
            request_id=request_id,
            prompt=prompt,
            generation_type="content",
            priority=2
        )
        
        if self.generation_worker.add_request(request):
            self.active_requests.add(request_id)
            return request_id
        
        return ""
    
    def rephrase_text(self, text: str, instructions: str = "") -> str:
        """
        Rephrase text with optional instructions (synchronous).
        
        Args:
            text: Text to rephrase
            instructions: Rephrasing instructions
            
        Returns:
            Rephrase request ID
        """
        request_id = str(uuid.uuid4())
        request = GenerationRequest(
            request_id=request_id,
            prompt=text,
            generation_type="rephrase",
            parameters={"instructions": instructions},
            priority=2
        )
        
        if self.generation_worker.add_request(request):
            self.active_requests.add(request_id)
            return request_id
        
        return ""
    
    def toggle_enabled(self) -> None:
        """Toggle assistant enabled state."""
        self.enabled = not self.enabled
        
        if self.enabled:
            self._update_status("Assistant enabled")
            if self.auto_completion_enabled:
                self._start_monitoring()
        else:
            self._update_status("Assistant disabled")
            self._stop_monitoring()
    
    def enable_auto_completion(self) -> None:
        """Enable automatic completion."""
        self.auto_completion_enabled = True
        if self.enabled:
            self._start_monitoring()
    
    def disable_auto_completion(self) -> None:
        """Disable automatic completion."""
        self.auto_completion_enabled = False
        self._stop_monitoring()
    
    def clear_buffer(self) -> None:
        """Clear the text buffer."""
        self.text_buffer.clear()
        self._update_status("Buffer cleared")
    
    def get_status(self) -> dict:
        """
        Get assistant status information.
        
        Returns:
            Status dictionary
        """
        return {
            'enabled': self.enabled,
            'auto_completion_enabled': self.auto_completion_enabled,
            'monitoring_active': self.keyboard_monitor.running,
            'llm_available': llm_manager.is_available(),
            'current_status': self.current_status,
            'active_requests': len(self.active_requests),
            'buffer_stats': self.text_buffer.get_stats(),
            'worker_stats': self.generation_worker.get_statistics()
        }
    
    def cleanup(self) -> None:
        """Clean up resources."""
        # Stop monitoring
        if self.keyboard_monitor:
            self.keyboard_monitor.stop()
        
        # Stop generation worker
        if self.generation_worker:
            self.generation_worker.stop()
            self.generation_worker.wait()
        
        # Clean up text buffer
        if self.text_buffer:
            self.text_buffer.cleanup()
        
        # Clean up LLM manager
        llm_manager.cleanup()
        
        self._update_status("Cleaned up")
