"""
Optimized text buffer for capturing and managing typing context.
"""

import time
from threading import Lock, Timer
from typing import Optional, Callable, List
import re

from ..utils.config import config


class TextBuffer:
    """
    Thread-safe text buffer that captures typing context and triggers
    completion suggestions intelligently.
    """
    
    def __init__(self, 
                 max_size: Optional[int] = None,
                 debounce_delay: Optional[float] = None,
                 on_context_ready: Optional[Callable[[str], None]] = None):
        """
        Initialize text buffer.
        
        Args:
            max_size: Maximum buffer size in characters
            debounce_delay: Delay in seconds before triggering context ready
            on_context_ready: Callback when context is ready for processing
        """
        self.max_size = max_size or config.get('text.buffer_size', 2000)
        self.debounce_delay = debounce_delay or config.get('text.debounce_delay', 500) / 1000.0
        self.min_context_length = config.get('text.min_context_length', 10)
        self.max_context_length = config.get('text.max_context_length', 500)
        
        self.buffer = ""
        self.lock = Lock()
        self.on_context_ready = on_context_ready
        self.debounce_timer: Optional[Timer] = None
        self.last_update_time = 0
        
        # Track typing patterns
        self.word_count = 0
        self.sentence_count = 0
        
    def append(self, text: str) -> None:
        """
        Append text to buffer with intelligent triggering.
        
        Args:
            text: Text to append
        """
        with self.lock:
            self.buffer += text
            self.last_update_time = time.time()
            
            # Trim buffer if too large
            if len(self.buffer) > self.max_size:
                # Keep the last portion, but try to keep complete words
                excess = len(self.buffer) - self.max_size
                trim_point = self.buffer.find(' ', excess)
                if trim_point != -1:
                    self.buffer = self.buffer[trim_point + 1:]
                else:
                    self.buffer = self.buffer[excess:]
            
            # Update statistics
            self._update_stats()
            
            # Cancel previous timer
            if self.debounce_timer:
                self.debounce_timer.cancel()
            
            # Check if we should trigger immediately
            if self._should_trigger_immediately(text):
                self._trigger_context_ready()
            else:
                # Set debounced trigger
                self.debounce_timer = Timer(self.debounce_delay, self._trigger_context_ready)
                self.debounce_timer.start()
    
    def _should_trigger_immediately(self, text: str) -> bool:
        """
        Determine if context should be processed immediately.
        
        Args:
            text: Recently added text
            
        Returns:
            True if should trigger immediately
        """
        # Trigger on sentence endings
        if text in '.!?':
            return True
        
        # Trigger on paragraph breaks
        if '\n\n' in text:
            return True
        
        # Trigger after certain punctuation followed by space
        if len(self.buffer) >= 2:
            last_two = self.buffer[-2:]
            if last_two in ['. ', '! ', '? ', ': ', '; ']:
                return True
        
        return False
    
    def _update_stats(self) -> None:
        """Update buffer statistics."""
        # Count words (simple approximation)
        words = re.findall(r'\b\w+\b', self.buffer)
        self.word_count = len(words)
        
        # Count sentences
        sentences = re.split(r'[.!?]+', self.buffer)
        self.sentence_count = len([s for s in sentences if s.strip()])
    
    def _trigger_context_ready(self) -> None:
        """Trigger context ready callback if conditions are met."""
        if not self.on_context_ready:
            return
        
        context = self.get_context()
        if context and len(context.strip()) >= self.min_context_length:
            try:
                self.on_context_ready(context)
            except Exception as e:
                print(f"Error in context ready callback: {e}")
    
    def get(self) -> str:
        """
        Get the full buffer content.
        
        Returns:
            Buffer content
        """
        with self.lock:
            return self.buffer
    
    def get_context(self) -> str:
        """
        Get intelligent context for completion.
        
        Returns:
            Context string optimized for LLM processing
        """
        with self.lock:
            if not self.buffer:
                return ""
            
            # Get the most relevant context
            context = self.buffer
            
            # If buffer is too long, get the last meaningful portion
            if len(context) > self.max_context_length:
                # Try to find a good breaking point (sentence or paragraph)
                break_points = []
                
                # Look for sentence endings
                for match in re.finditer(r'[.!?]\s+', context):
                    break_points.append(match.end())
                
                # Look for paragraph breaks
                for match in re.finditer(r'\n\s*\n', context):
                    break_points.append(match.end())
                
                if break_points:
                    # Find the best break point
                    target_start = len(context) - self.max_context_length
                    best_break = min(break_points, 
                                   key=lambda x: abs(x - target_start))
                    if best_break > target_start:
                        context = context[best_break:]
                
                # If no good break point, just truncate
                if len(context) > self.max_context_length:
                    context = context[-self.max_context_length:]
            
            return context.strip()
    
    def get_last_sentence(self) -> str:
        """
        Get the last complete sentence from buffer.
        
        Returns:
            Last sentence or empty string
        """
        with self.lock:
            if not self.buffer:
                return ""
            
            # Find sentence boundaries
            sentences = re.split(r'([.!?])', self.buffer)
            
            # Reconstruct sentences
            complete_sentences = []
            for i in range(0, len(sentences) - 1, 2):
                if i + 1 < len(sentences):
                    sentence = sentences[i] + sentences[i + 1]
                    if sentence.strip():
                        complete_sentences.append(sentence.strip())
            
            return complete_sentences[-1] if complete_sentences else ""
    
    def get_last_words(self, count: int = 10) -> str:
        """
        Get the last N words from buffer.
        
        Args:
            count: Number of words to get
            
        Returns:
            Last words as string
        """
        with self.lock:
            if not self.buffer:
                return ""
            
            words = re.findall(r'\S+', self.buffer)
            last_words = words[-count:] if len(words) >= count else words
            return ' '.join(last_words)
    
    def clear(self) -> None:
        """Clear the buffer."""
        with self.lock:
            self.buffer = ""
            self.word_count = 0
            self.sentence_count = 0
            
            # Cancel any pending timer
            if self.debounce_timer:
                self.debounce_timer.cancel()
                self.debounce_timer = None
    
    def set_context_callback(self, callback: Callable[[str], None]) -> None:
        """
        Set the context ready callback.
        
        Args:
            callback: Function to call when context is ready
        """
        self.on_context_ready = callback
    
    def get_stats(self) -> dict:
        """
        Get buffer statistics.
        
        Returns:
            Dictionary with buffer stats
        """
        with self.lock:
            return {
                'length': len(self.buffer),
                'word_count': self.word_count,
                'sentence_count': self.sentence_count,
                'last_update': self.last_update_time
            }
    
    def cleanup(self) -> None:
        """Clean up resources."""
        if self.debounce_timer:
            self.debounce_timer.cancel()
            self.debounce_timer = None
