"""
Auto-write dialog for generating content from prompts.
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, 
                            QPushButton, QLabel, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer

from .styles import styles
from ..utils.clipboard import ClipboardManager


class AutoWriteDialog(QWidget):
    """Dialog for generating content based on user prompts."""
    
    # Signals
    content_generated = pyqtSignal(str)
    dialog_closed = pyqtSignal()
    
    def __init__(self, parent=None):
        """
        Initialize auto-write dialog.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Widget configuration
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Clipboard manager
        self.clipboard_manager = ClipboardManager()
        
        # State
        self.is_generating = False
        self.current_request_id = ""
        
        # Setup UI
        self._setup_ui()
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        self._create_header(layout)
        
        # Prompt input
        self._create_prompt_input(layout)
        
        # Progress indicator
        self._create_progress_indicator(layout)
        
        # Action buttons
        self._create_action_buttons(layout)
        
        # Apply styles
        self.setStyleSheet(styles.floating_widget)
        
        # Set size - made larger for better UX
        self.setFixedSize(600, 500)
        
        # Initially hidden
        self.hide()
    
    def _create_header(self, layout: QVBoxLayout) -> None:
        """Create the header with title and close button."""
        header = QHBoxLayout()
        
        # Title
        title = QLabel("✨ Auto Write")
        title.setStyleSheet(styles.title_label)
        header.addWidget(title)
        
        # Close button
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.clicked.connect(self.hide)
        close_btn.setStyleSheet(styles.close_button)
        header.addWidget(close_btn)
        
        layout.addLayout(header)
    
    def _create_prompt_input(self, layout: QVBoxLayout) -> None:
        """Create the prompt input area."""
        # Label
        label = QLabel("What would you like to write about?")
        label.setStyleSheet(styles.label)
        layout.addWidget(label)
        
        # Prompt input
        self.prompt_input = QTextEdit()
        self.prompt_input.setPlaceholderText(
            "Describe what you want to write about...\n\n"
            "Examples:\n"
            "• A professional email about project updates\n"
            "• An introduction paragraph for a blog post about AI\n"
            "• A summary of the benefits of remote work\n"
            "• A creative story about a time traveler"
        )
        self.prompt_input.setMinimumHeight(150)
        self.prompt_input.setStyleSheet(styles.text_edit)
        layout.addWidget(self.prompt_input)
        
        # Tips
        tips_label = QLabel("💡 Tip: Be specific about tone, length, and style for better results")
        tips_label.setStyleSheet(f"""
            QLabel {{
                color: {styles.text_muted};
                font-size: 12px;
                font-style: italic;
                padding: 5px;
                background-color: transparent;
                border: none;
            }}
        """)
        layout.addWidget(tips_label)
    
    def _create_progress_indicator(self, layout: QVBoxLayout) -> None:
        """Create the progress indicator."""
        self.progress_widget = QWidget()
        progress_layout = QVBoxLayout(self.progress_widget)
        progress_layout.setContentsMargins(0, 0, 0, 0)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(0)  # Indeterminate
        self.progress_bar.setStyleSheet(styles.progress_bar)
        progress_layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Generating content...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet(styles.label)
        progress_layout.addWidget(self.status_label)
        
        layout.addWidget(self.progress_widget)
        
        # Initially hidden
        self.progress_widget.hide()
    
    def _create_action_buttons(self, layout: QVBoxLayout) -> None:
        """Create the action buttons."""
        buttons_layout = QHBoxLayout()
        
        # Cancel button
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self._cancel_generation)
        self.cancel_btn.setStyleSheet(styles.secondary_button)
        buttons_layout.addWidget(self.cancel_btn)
        
        # Generate button
        self.generate_btn = QPushButton("✨ Generate")
        self.generate_btn.clicked.connect(self._generate_content)
        self.generate_btn.setStyleSheet(styles.primary_button)
        buttons_layout.addWidget(self.generate_btn)
        
        layout.addLayout(buttons_layout)
    
    def _generate_content(self) -> None:
        """Start content generation."""
        prompt = self.prompt_input.toPlainText().strip()
        
        if not prompt:
            self.status_label.setText("Please enter a prompt")
            return
        
        if self.is_generating:
            return
        
        # Update UI state
        self.is_generating = True
        self.generate_btn.setEnabled(False)
        self.prompt_input.setEnabled(False)
        self.progress_widget.show()
        
        # Get assistant from parent
        if hasattr(self.parent(), 'get_assistant'):
            assistant = self.parent().get_assistant()
            
            # Request content generation
            self.current_request_id = assistant.generate_content(prompt)
            
            if self.current_request_id:
                # Connect to completion signals
                assistant.generation_completed.connect(self._handle_generation_completed)
                assistant.generation_failed.connect(self._handle_generation_failed)
                assistant.generation_started.connect(self._handle_generation_started)

                # Update status
                self.status_label.setText("Generating content...")

                # Start timeout timer (30 seconds)
                self.timeout_timer = QTimer()
                self.timeout_timer.timeout.connect(self._handle_timeout)
                self.timeout_timer.setSingleShot(True)
                self.timeout_timer.start(30000)
            else:
                self._handle_generation_failed("", "Failed to queue generation request")
        else:
            self._handle_generation_failed("", "Assistant not available")
    
    def _cancel_generation(self) -> None:
        """Cancel ongoing generation."""
        if self.is_generating and self.current_request_id:
            # Cancel the request
            if hasattr(self.parent(), 'get_assistant'):
                assistant = self.parent().get_assistant()
                assistant.generation_worker.cancel_request(self.current_request_id)
        
        self._reset_ui_state()
        self.hide()
    
    def _handle_generation_started(self, request_id: str) -> None:
        """
        Handle generation started.

        Args:
            request_id: Request identifier
        """
        if request_id == self.current_request_id:
            self.status_label.setText("AI is thinking...")

    def _handle_generation_completed(self, request_id: str, result: str) -> None:
        """
        Handle generation completion.

        Args:
            request_id: Request identifier
            result: Generated content
        """
        # Only handle our request
        if request_id != self.current_request_id:
            return

        self._reset_ui_state()

        # Disconnect signals to avoid multiple connections
        self._disconnect_signals()

        if result.strip():
            # Copy result to clipboard
            self.clipboard_manager.set_clipboard_text(result.strip())
            self.content_generated.emit(result.strip())
            self.status_label.setText("Content copied to clipboard!")

            # Hide after a short delay to show the success message
            QTimer.singleShot(1500, self.hide)
        else:
            self.status_label.setText("Generated empty content")
            QTimer.singleShot(2000, self.hide)
    
    def _handle_generation_failed(self, request_id: str, error: str) -> None:
        """
        Handle generation failure.

        Args:
            request_id: Request identifier
            error: Error message
        """
        # Only handle our request
        if request_id != self.current_request_id and request_id != "":
            return

        self._reset_ui_state()

        # Disconnect signals to avoid multiple connections
        self._disconnect_signals()

        # Show error
        self.status_label.setText(f"Failed: {error}")

        # Hide after a delay
        QTimer.singleShot(3000, self.hide)
    
    def _handle_timeout(self) -> None:
        """Handle generation timeout."""
        self._handle_generation_failed(self.current_request_id, "Generation timed out")

    def _disconnect_signals(self) -> None:
        """Disconnect all assistant signals to avoid multiple connections."""
        if hasattr(self.parent(), 'get_assistant'):
            assistant = self.parent().get_assistant()
            try:
                assistant.generation_completed.disconnect(self._handle_generation_completed)
                assistant.generation_failed.disconnect(self._handle_generation_failed)
                assistant.generation_started.disconnect(self._handle_generation_started)
            except TypeError:
                # Signals might not be connected, ignore
                pass
    
    def _reset_ui_state(self) -> None:
        """Reset UI to normal state."""
        self.is_generating = False
        self.current_request_id = ""
        
        # Reset UI elements
        self.generate_btn.setEnabled(True)
        self.prompt_input.setEnabled(True)
        self.progress_widget.hide()
        
        # Stop timeout timer
        if hasattr(self, 'timeout_timer'):
            self.timeout_timer.stop()
    
    def keyPressEvent(self, event) -> None:
        """Handle key press events."""
        if event.key() == Qt.Key_Escape:
            if self.is_generating:
                self._cancel_generation()
            else:
                self.hide()
        elif event.key() == Qt.Key_Return and event.modifiers() == Qt.ControlModifier:
            # Ctrl+Enter to generate
            if not self.is_generating:
                self._generate_content()
        else:
            super().keyPressEvent(event)
    
    def showEvent(self, event) -> None:
        """Handle show event."""
        super().showEvent(event)
        
        # Clear previous content
        self.prompt_input.clear()
        self._reset_ui_state()
        
        # Set focus to prompt input
        self.prompt_input.setFocus()
        
        # Ensure dialog is on top
        self.raise_()
        self.activateWindow()
    
    def hideEvent(self, event) -> None:
        """Handle hide event."""
        super().hideEvent(event)

        # Cancel any ongoing generation
        if self.is_generating:
            self._cancel_generation()

        # Disconnect signals and reset state
        self._disconnect_signals()
        self._reset_ui_state()

        # Emit closed signal
        self.dialog_closed.emit()
    
    def show_at_position(self, x: int, y: int) -> None:
        """
        Show dialog at specific position.
        
        Args:
            x: X coordinate
            y: Y coordinate
        """
        self.move(x, y)
        self.show()
    
    def get_prompt(self) -> str:
        """
        Get the current prompt text.
        
        Returns:
            Prompt text
        """
        return self.prompt_input.toPlainText().strip()
    
    def set_prompt(self, prompt: str) -> None:
        """
        Set the prompt text.
        
        Args:
            prompt: Prompt text to set
        """
        self.prompt_input.setPlainText(prompt)
    
    def clear_prompt(self) -> None:
        """Clear the prompt input."""
        self.prompt_input.clear()
