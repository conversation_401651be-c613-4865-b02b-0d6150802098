"""
Optimized LLM Manager for Quill AI Writing Assistant.
Handles all LLM interactions with performance optimizations.
"""

import asyncio
import time
from typing import Dict, List, Optional, Callable, Any
from threading import Lock
import requests
import json
from functools import lru_cache
import hashlib

from ..utils.config import config


class LLMCache:
    """Simple LRU cache for LLM responses."""
    
    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self.cache: Dict[str, tuple] = {}  # key -> (response, timestamp)
        self.access_order: List[str] = []
        self.lock = Lock()
    
    def _generate_key(self, prompt: str, model: str) -> str:
        """Generate cache key from prompt and model."""
        content = f"{model}:{prompt}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def get(self, prompt: str, model: str) -> Optional[str]:
        """Get cached response if available and not expired."""
        key = self._generate_key(prompt, model)
        
        with self.lock:
            if key in self.cache:
                response, timestamp = self.cache[key]
                # Cache expires after 1 hour
                if time.time() - timestamp < 3600:
                    # Move to end (most recently used)
                    self.access_order.remove(key)
                    self.access_order.append(key)
                    return response
                else:
                    # Expired, remove from cache
                    del self.cache[key]
                    self.access_order.remove(key)
        
        return None
    
    def set(self, prompt: str, model: str, response: str) -> None:
        """Cache the response."""
        key = self._generate_key(prompt, model)
        
        with self.lock:
            # Remove if already exists
            if key in self.cache:
                self.access_order.remove(key)
            
            # Add new entry
            self.cache[key] = (response, time.time())
            self.access_order.append(key)
            
            # Evict oldest if cache is full
            while len(self.cache) > self.max_size:
                oldest_key = self.access_order.pop(0)
                del self.cache[oldest_key]


class LLMManager:
    """Optimized LLM manager with caching and connection pooling."""
    
    def __init__(self):
        self.base_url = config.get('llm.base_url', 'http://localhost:11434')
        self.model = config.get('llm.model', 'hf.co/unsloth/Qwen3-0.6B-GGUF:Q4_K_M')
        self.timeout = config.get('llm.timeout', 30)
        self.cache = LLMCache(config.get('performance.cache_size', 100))
        
        # Connection session for reuse
        self.session = requests.Session()
        self.session.timeout = self.timeout
        
        # Model parameters
        self.default_params = {
            'temperature': config.get('llm.temperature', 0.7),
            'top_p': config.get('llm.top_p', 0.9),
            'max_tokens': config.get('llm.max_tokens', 512),
            'num_ctx': config.get('llm.num_ctx', 2048),
            'num_predict': config.get('llm.num_predict', 256),
            'repeat_penalty': config.get('llm.repeat_penalty', 1.1),
            'stream': False  # We'll handle streaming separately
        }
        
        # Ensure model is loaded
        self._ensure_model_loaded()
    
    def _ensure_model_loaded(self) -> bool:
        """Ensure the model is loaded in Ollama."""
        try:
            # Check if model is already loaded
            response = self.session.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [m['name'] for m in models]
                
                if self.model not in model_names:
                    print(f"Model {self.model} not found. Pulling...")
                    # Pull the model
                    pull_response = self.session.post(
                        f"{self.base_url}/api/pull",
                        json={'name': self.model}
                    )
                    if pull_response.status_code != 200:
                        print(f"Failed to pull model: {pull_response.text}")
                        return False
                
                return True
        except Exception as e:
            print(f"Error ensuring model is loaded: {e}")
            return False
    
    def generate(self, prompt: str, **kwargs) -> str:
        """
        Generate text using the LLM.
        
        Args:
            prompt: Input prompt
            **kwargs: Additional parameters to override defaults
            
        Returns:
            Generated text
        """
        # Check cache first
        cached_response = self.cache.get(prompt, self.model)
        if cached_response:
            return cached_response
        
        # Prepare parameters
        params = self.default_params.copy()
        params.update(kwargs)
        
        try:
            # Make request to Ollama
            payload = {
                'model': self.model,
                'prompt': prompt,
                'options': {
                    'temperature': params['temperature'],
                    'top_p': params['top_p'],
                    'num_ctx': params['num_ctx'],
                    'num_predict': params['num_predict'],
                    'repeat_penalty': params['repeat_penalty']
                },
                'stream': False
            }
            
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result.get('response', '').strip()
                
                # Cache the response
                self.cache.set(prompt, self.model, generated_text)
                
                return generated_text
            else:
                print(f"LLM request failed: {response.status_code} - {response.text}")
                return ""
                
        except Exception as e:
            print(f"Error generating text: {e}")
            return ""
    
    def generate_completion(self, context: str) -> str:
        """Generate text completion based on context."""
        prompt = f"""You are an intelligent writing assistant. Complete the following text naturally and concisely. Provide only the continuation, no explanations or additional text.

Context: {context}

Continuation:"""
        
        return self.generate(prompt, temperature=0.8, num_predict=128)
    
    def rephrase_text(self, text: str, instructions: str = "") -> str:
        """Rephrase text according to instructions."""
        if instructions:
            prompt = f"""Rephrase the following text according to these instructions: {instructions}

Original text: {text}

Rephrased text:"""
        else:
            prompt = f"""Rephrase the following text to make it clearer and more engaging while maintaining the original meaning:

Original text: {text}

Rephrased text:"""
        
        return self.generate(prompt, temperature=0.7, num_predict=256)
    
    def generate_content(self, topic: str) -> str:
        """Generate content based on a topic or prompt."""
        prompt = f"""Write engaging and informative content about the following topic. Be creative and provide valuable insights:

Topic: {topic}

Content:"""
        
        return self.generate(prompt, temperature=0.8, num_predict=512)
    
    def is_available(self) -> bool:
        """Check if the LLM service is available."""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model."""
        try:
            response = self.session.post(
                f"{self.base_url}/api/show",
                json={'name': self.model},
                timeout=10
            )
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return {}
    
    def cleanup(self) -> None:
        """Clean up resources."""
        if hasattr(self, 'session'):
            self.session.close()


# Global LLM manager instance
llm_manager = LLMManager()
