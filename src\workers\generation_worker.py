"""
Async text generation worker for non-blocking LLM operations.
"""

from PyQt5.QtCore import QThread, pyqtSignal, QMutex, QWaitCondition
from typing import Optional, Dict, Any
import time
import queue
import threading

from ..core.llm_manager import llm_manager


class GenerationRequest:
    """Represents a text generation request."""
    
    def __init__(self, 
                 request_id: str,
                 prompt: str,
                 generation_type: str = "completion",
                 parameters: Optional[Dict[str, Any]] = None,
                 priority: int = 1):
        """
        Initialize generation request.
        
        Args:
            request_id: Unique identifier for the request
            prompt: Text prompt for generation
            generation_type: Type of generation (completion, rephrase, content)
            parameters: Additional parameters for generation
            priority: Request priority (lower = higher priority)
        """
        self.request_id = request_id
        self.prompt = prompt
        self.generation_type = generation_type
        self.parameters = parameters or {}
        self.priority = priority
        self.timestamp = time.time()


class GenerationWorker(QThread):
    """
    Background worker for handling text generation requests.
    Processes requests asynchronously to keep UI responsive.
    """
    
    # Signals
    generation_completed = pyqtSignal(str, str)  # request_id, result
    generation_failed = pyqtSignal(str, str)     # request_id, error
    generation_started = pyqtSignal(str)         # request_id
    queue_status_changed = pyqtSignal(int)       # queue_size
    
    def __init__(self, max_queue_size: int = 10):
        """
        Initialize generation worker.
        
        Args:
            max_queue_size: Maximum number of queued requests
        """
        super().__init__()
        
        self.max_queue_size = max_queue_size
        self.request_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self.running = False
        self.current_request: Optional[GenerationRequest] = None
        
        # Thread synchronization
        self.mutex = QMutex()
        self.condition = QWaitCondition()
        
        # Statistics
        self.processed_requests = 0
        self.failed_requests = 0
        self.total_processing_time = 0.0
        
        # Request tracking
        self.active_requests = set()
    
    def run(self) -> None:
        """Main worker thread loop."""
        self.running = True
        
        while self.running:
            try:
                # Wait for requests with timeout
                try:
                    priority, request = self.request_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                if not self.running:
                    break
                
                self.current_request = request
                self.active_requests.add(request.request_id)
                
                # Emit started signal
                self.generation_started.emit(request.request_id)
                
                # Process the request
                start_time = time.time()
                success = self._process_request(request)
                processing_time = time.time() - start_time
                
                # Update statistics
                self.total_processing_time += processing_time
                if success:
                    self.processed_requests += 1
                else:
                    self.failed_requests += 1
                
                # Clean up
                self.active_requests.discard(request.request_id)
                self.current_request = None
                
                # Update queue status
                self.queue_status_changed.emit(self.request_queue.qsize())
                
            except Exception as e:
                print(f"Error in generation worker: {e}")
                if self.current_request:
                    self.generation_failed.emit(
                        self.current_request.request_id,
                        f"Worker error: {str(e)}"
                    )
                    self.active_requests.discard(self.current_request.request_id)
                    self.current_request = None
    
    def _process_request(self, request: GenerationRequest) -> bool:
        """
        Process a single generation request.
        
        Args:
            request: Request to process
            
        Returns:
            True if successful
        """
        try:
            # Check if LLM is available
            if not llm_manager.is_available():
                self.generation_failed.emit(
                    request.request_id,
                    "LLM service is not available"
                )
                return False
            
            # Generate based on type
            result = ""
            
            if request.generation_type == "completion":
                result = llm_manager.generate_completion(request.prompt)
            elif request.generation_type == "rephrase":
                instructions = request.parameters.get("instructions", "")
                result = llm_manager.rephrase_text(request.prompt, instructions)
            elif request.generation_type == "content":
                result = llm_manager.generate_content(request.prompt)
            else:
                # Generic generation
                result = llm_manager.generate(request.prompt, **request.parameters)
            
            if result:
                self.generation_completed.emit(request.request_id, result)
                return True
            else:
                self.generation_failed.emit(
                    request.request_id,
                    "Empty response from LLM"
                )
                return False
                
        except Exception as e:
            self.generation_failed.emit(
                request.request_id,
                f"Generation error: {str(e)}"
            )
            return False
    
    def add_request(self, request: GenerationRequest) -> bool:
        """
        Add a generation request to the queue.
        
        Args:
            request: Request to add
            
        Returns:
            True if added successfully
        """
        try:
            # Check if queue is full
            if self.request_queue.full():
                # Remove oldest low-priority request
                self._remove_low_priority_request()
            
            # Add request with priority
            self.request_queue.put((request.priority, request), block=False)
            self.queue_status_changed.emit(self.request_queue.qsize())
            
            return True
            
        except queue.Full:
            print("Generation queue is full, request dropped")
            return False
        except Exception as e:
            print(f"Error adding request to queue: {e}")
            return False
    
    def _remove_low_priority_request(self) -> None:
        """Remove the oldest low-priority request from queue."""
        try:
            # Create a temporary list to hold requests
            temp_requests = []
            removed = False
            
            # Extract all requests
            while not self.request_queue.empty():
                try:
                    priority, request = self.request_queue.get_nowait()
                    temp_requests.append((priority, request))
                except queue.Empty:
                    break
            
            # Sort by priority and timestamp, remove the lowest priority oldest request
            temp_requests.sort(key=lambda x: (x[0], x[1].timestamp), reverse=True)
            
            if temp_requests:
                # Remove the last (lowest priority, oldest) request
                removed_request = temp_requests.pop()
                print(f"Removed low-priority request: {removed_request[1].request_id}")
                removed = True
            
            # Put remaining requests back
            for priority, request in temp_requests:
                self.request_queue.put((priority, request))
                
        except Exception as e:
            print(f"Error removing low-priority request: {e}")
    
    def cancel_request(self, request_id: str) -> bool:
        """
        Cancel a pending request.
        
        Args:
            request_id: ID of request to cancel
            
        Returns:
            True if cancelled
        """
        try:
            # If it's the current request, we can't cancel it
            if self.current_request and self.current_request.request_id == request_id:
                return False
            
            # Remove from queue
            temp_requests = []
            found = False
            
            while not self.request_queue.empty():
                try:
                    priority, request = self.request_queue.get_nowait()
                    if request.request_id != request_id:
                        temp_requests.append((priority, request))
                    else:
                        found = True
                except queue.Empty:
                    break
            
            # Put remaining requests back
            for priority, request in temp_requests:
                self.request_queue.put((priority, request))
            
            if found:
                self.queue_status_changed.emit(self.request_queue.qsize())
            
            return found
            
        except Exception as e:
            print(f"Error cancelling request: {e}")
            return False
    
    def stop(self) -> None:
        """Stop the worker thread."""
        self.running = False
        
        # Clear the queue
        while not self.request_queue.empty():
            try:
                self.request_queue.get_nowait()
            except queue.Empty:
                break
        
        # Wait for current request to finish (with timeout)
        if self.current_request:
            self.wait(5000)  # 5 second timeout
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get worker statistics.
        
        Returns:
            Statistics dictionary
        """
        avg_processing_time = 0.0
        if self.processed_requests > 0:
            avg_processing_time = self.total_processing_time / self.processed_requests
        
        return {
            'running': self.running,
            'queue_size': self.request_queue.qsize(),
            'max_queue_size': self.max_queue_size,
            'processed_requests': self.processed_requests,
            'failed_requests': self.failed_requests,
            'average_processing_time': avg_processing_time,
            'current_request': self.current_request.request_id if self.current_request else None,
            'active_requests': len(self.active_requests)
        }
    
    def clear_queue(self) -> int:
        """
        Clear all pending requests.
        
        Returns:
            Number of requests cleared
        """
        cleared = 0
        
        while not self.request_queue.empty():
            try:
                self.request_queue.get_nowait()
                cleared += 1
            except queue.Empty:
                break
        
        self.queue_status_changed.emit(0)
        return cleared
